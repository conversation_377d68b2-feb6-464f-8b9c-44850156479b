
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named urllib.urlopen - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.urlencode - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named org - imported by pickle (optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), webbrowser (delayed), psutil (optional), netrc (delayed, conditional), getpass (delayed), _pytest._py.path (delayed), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), twisted.python.util (optional), setuptools._distutils.archive_util (optional), setuptools._distutils.util (delayed, conditional, optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), _pytest._py.path (delayed), distutils.archive_util (optional), twisted.python.util (optional), setuptools._distutils.archive_util (optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), fsspec.asyn (conditional, optional), joblib.externals.loky.backend.fork_exec (delayed, optional), torch._inductor.codecache (delayed, conditional), twisted.internet.process (delayed, optional)
missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), getpass (optional), absl.flags._helpers (optional), tqdm.utils (delayed, optional), click._termui_impl (conditional), invoke.runners (optional), invoke.terminals (conditional), astropy.utils.console (delayed, optional), twisted.internet.process (optional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named pickle5 - imported by numpy.compat.py3k (optional), srsly.cloudpickle.compat (conditional, optional)
missing module named _dummy_thread - imported by numpy.core.arrayprint (optional), cffi.lock (conditional, optional), torch._jit_internal (optional), sortedcontainers.sortedlist (conditional, optional), astropy.extern._strptime (optional)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed), scipy.optimize._optimize (top-level), scipy.linalg._decomp (top-level), scipy.optimize._minpack_py (top-level), scipy.interpolate._pade (top-level), scipy.signal._lti_conversion (top-level)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.max - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.prod - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), scipy.stats._stats_py (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_schur (top-level), scipy.sparse.linalg._isolve.utils (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.optimize._tnc (top-level), scipy.optimize._slsqp_py (top-level), scipy.interpolate._interpolate (top-level), scipy.interpolate._fitpack_impl (top-level), scipy.interpolate._fitpack2 (top-level), scipy.integrate._ode (top-level), nltk.parse.transitionparser (optional), scipy.io._netcdf (top-level), pygame.surfarray (top-level), scipy._lib._finite_differences (top-level), scipy.stats._morestats (top-level), scipy.signal._bsplines (top-level), scipy.signal._filter_design (top-level), scipy.signal._lti_conversion (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), scipy.optimize._minpack_py (top-level), scipy.io._netcdf (top-level), torch._dynamo.variables.misc (optional), gensim.models.keyedvectors (top-level), gensim.models.doc2vec (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), pandas.compat.numpy.function (top-level), _pytest.python_api (conditional), pytesseract.pytesseract (optional), scipy.io._mmio (top-level), pygame.surfarray (top-level), spacy.tokens._serialize (top-level), imageio.typing (optional), scipy.stats._distn_infrastructure (top-level), scipy.stats._mstats_basic (top-level), scipy.stats._mstats_extras (top-level), astropy.cosmology.funcs.comparison (top-level), gensim.models.keyedvectors (top-level)
missing module named pyodide_js - imported by threadpoolctl (delayed, optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional), pkg_resources._vendor.appdirs (delayed, conditional), pygments.formatters.img (optional)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level), numpy.testing.overrides (top-level)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), _pytest.capture (delayed, optional), site (delayed, optional), rlcompleter (optional), pstats (conditional, optional), sympy.interactive.session (delayed, optional), tensorflow.python.debug.cli.readline_ui (top-level), sphinx.cmd.quickstart (optional)
missing module named numpy.isinf - imported by numpy (top-level), numpy.testing._private.utils (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.isnan - imported by numpy (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.isfinite - imported by numpy (top-level), numpy.testing._private.utils (top-level), scipy.linalg._decomp (top-level), scipy.linalg._matfuncs (top-level), scipy.optimize._slsqp_py (top-level)
missing module named asyncio._set_event_loop_policy - imported by asyncio (optional), mock.backports (optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), joblib.externals.loky.backend.context (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level), joblib.parallel (top-level)
missing module named fcntl - imported by subprocess (optional), xmlrpc.server (optional), pyglet.libs.ioctl (top-level), absl.flags._helpers (optional), tqdm.utils (delayed, optional), pty (delayed, optional), filelock._unix (conditional, optional), invoke.runners (optional), invoke.terminals (conditional), paramiko.agent (delayed), astropy.utils.console (optional), twisted.python.compat (delayed, optional), twisted.internet.fdesc (optional), twisted.internet.process (optional), torch.testing._internal.distributed.distributed_test (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.ProcessError - imported by multiprocessing (top-level), gensim.models.ensemblelda (top-level)
missing module named multiprocessing.Pipe - imported by multiprocessing (top-level), gensim.models.ensemblelda (top-level), memory_profiler (optional)
missing module named multiprocessing.Queue - imported by multiprocessing (top-level), gensim.models.ldamulticore (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), transformers.data.processors.squad (top-level), gensim.models.ldamulticore (top-level)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), wasabi.printer (top-level), gensim.models.ensemblelda (top-level), memory_profiler (optional)
missing module named multiprocessing.Pool - imported by multiprocessing (top-level), transformers.models.nougat.tokenization_nougat_fast (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level), transformers.data.processors.squad (top-level), scipy._lib._util (delayed, conditional), gensim.models.ldamulticore (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level), torch._dynamo.variables.misc (optional)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level), gensim.models.doc2vec (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level), torch._dynamo.variables.misc (optional)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.stats._mstats_extras (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level), gensim.models.word2vec (top-level), gensim.models.keyedvectors (top-level), gensim.models.fasttext (top-level), gensim.models.doc2vec (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level), pygame.surfarray (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_ldl (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level), scipy.stats._morestats (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._matfuncs (top-level), scipy.stats._morestats (top-level)
missing module named numpy.random.RandomState - imported by numpy.random (delayed), networkx.utils.backends (delayed)
missing module named railroad - imported by pyparsing.diagram (top-level), pkg_resources._vendor.pyparsing.diagram (top-level), setuptools._vendor.pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed), pydot.dot_parser (top-level)
missing module named 'pkg_resources.extern.pyparsing' - imported by pkg_resources._vendor.packaging.markers (top-level), pkg_resources._vendor.packaging.requirements (top-level)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), pkg_resources._vendor.packaging._manylinux (delayed, optional)
missing module named 'pkg_resources.extern.importlib_resources' - imported by pkg_resources._vendor.jaraco.text (optional)
missing module named 'pkg_resources.extern.more_itertools' - imported by pkg_resources._vendor.jaraco.functools (top-level)
missing module named 'com.sun' - imported by torch._appdirs (delayed, conditional, optional), pkg_resources._vendor.appdirs (delayed, conditional, optional)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.appdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.jaraco' - imported by pkg_resources (top-level), pkg_resources._vendor.jaraco.text (top-level)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level), astunparse (top-level), html5lib._inputstream (top-level), six.moves.urllib (top-level), html5lib.filters.sanitizer (top-level), tensorflow.python.distribute.multi_process_runner (top-level), tensorflow.python.distribute.coordinator.cluster_coordinator (top-level), pasta.base.annotate (top-level)
missing module named six.moves.zip - imported by six.moves (top-level), pasta.base.annotate (top-level)
missing module named six.moves.urllib.request - imported by six.moves.urllib (top-level), tensorflow.python.distribute.failure_handling.failure_handling_util (top-level)
missing module named six.moves.cStringIO - imported by six.moves (top-level), astunparse (top-level)
missing module named StringIO - imported by six (conditional), urllib3.packages.six (conditional), simplejson.compat (conditional, optional), nltk.corpus.reader.timit (delayed, optional), srsly.ruamel_yaml.compat (conditional), botocore.vendored.six (conditional)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named 'setuptools.extern.pyparsing' - imported by setuptools._vendor.packaging.requirements (top-level), setuptools._vendor.packaging.markers (top-level)
missing module named collections.Sequence - imported by collections (optional), sortedcontainers.sortedlist (optional), sortedcontainers.sortedset (optional), sortedcontainers.sorteddict (optional), setuptools._vendor.ordered_set (optional)
missing module named collections.MutableSet - imported by collections (conditional), srsly.ruamel_yaml.comments (conditional), sortedcontainers.sortedset (optional), setuptools._vendor.ordered_set (optional)
missing module named 'setuptools.extern.jaraco' - imported by setuptools._reqs (top-level), setuptools._entry_points (top-level), setuptools.command.egg_info (top-level), setuptools._vendor.jaraco.text (top-level)
missing module named setuptools.extern.importlib_resources - imported by setuptools.extern (conditional), setuptools._importlib (conditional), setuptools._vendor.jaraco.text (optional)
missing module named setuptools.extern.tomli - imported by setuptools.extern (delayed), setuptools.config.pyprojecttoml (delayed)
missing module named setuptools.extern.importlib_metadata - imported by setuptools.extern (conditional), setuptools._importlib (conditional)
missing module named setuptools.extern.ordered_set - imported by setuptools.extern (top-level), setuptools.dist (top-level)
missing module named setuptools.extern.packaging - imported by setuptools.extern (top-level), setuptools.dist (top-level), setuptools.command.egg_info (top-level), setuptools.depends (top-level)
missing module named 'setuptools.extern.more_itertools' - imported by setuptools.dist (top-level), setuptools.config.expand (delayed), setuptools._itertools (top-level), setuptools._entry_points (top-level), setuptools.command.build_py (top-level), setuptools.msvc (top-level), setuptools._vendor.jaraco.functools (top-level)
missing module named 'setuptools.extern.packaging.version' - imported by setuptools.config.setupcfg (top-level), setuptools.msvc (top-level)
missing module named 'setuptools.extern.packaging.utils' - imported by setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.tags' - imported by setuptools.wheel (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named 'setuptools.extern.packaging.specifiers' - imported by setuptools.config.setupcfg (top-level), setuptools.config._apply_pyprojecttoml (delayed)
missing module named 'setuptools.extern.packaging.requirements' - imported by setuptools.config.setupcfg (top-level)
missing module named collections.Callable - imported by collections (optional), cffi.api (optional), socks (optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional), sortedcontainers.sortedlist (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional), sortedcontainers.sortedlist (conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional), simplejson.compat (conditional, optional), xlrd.timemachine (conditional), srsly.ruamel_yaml.compat (conditional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional), spacy.compat (optional), astropy.extern.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named xmlrpclib - imported by defusedxml.xmlrpc (conditional)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level)
missing module named gi - imported by matplotlib.cbook (delayed, conditional), pyglet.media.codecs.gstreamer (optional)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional, optional), tqdm.version (optional)
missing module named regex.DEFAULT_VERSION - imported by regex (delayed, optional), regex.regex (delayed, optional)
missing module named nltk.corpus.CorpusReader - imported by nltk.corpus (delayed, conditional), nltk.corpus.reader.wordnet (delayed, conditional)
missing module named nltk.corpus.reader.CorpusReader - imported by nltk.corpus.reader (top-level), nltk.corpus.reader.wordnet (top-level), nltk.corpus.reader.lin (top-level), nltk.corpus.reader.sentiwordnet (top-level), nltk.corpus.reader.crubadan (top-level), nltk.corpus.reader.bcp47 (top-level)
missing module named OpenGL - imported by pygame (delayed)
missing module named pygame.register_quit - imported by pygame (top-level), pygame.fastevent (top-level)
missing module named pygame.error - imported by pygame (top-level), pygame.fastevent (top-level)
missing module named 'pygame.overlay' - imported by pygame (optional)
missing module named 'pygame.cdrom' - imported by pygame (conditional, optional)
missing module named ossaudiodev - imported by nltk.corpus.reader.timit (delayed, optional)
missing module named numpy.arccosh - imported by numpy (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.arcsinh - imported by numpy (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.tan - imported by numpy (top-level), scipy.signal._bsplines (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.double - imported by numpy (top-level), gensim.models.keyedvectors (top-level)
missing module named numpy.power - imported by numpy (top-level), scipy.stats._kde (top-level)
missing module named numpy.hypot - imported by numpy (top-level), scipy.stats._morestats (top-level)
missing module named numpy.sinh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.cosh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.tanh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.expm1 - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.log1p - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.ceil - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.log - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._morestats (top-level), astropy.cosmology.flrw.lambdacdm (top-level), scipy.signal._waveforms (top-level)
missing module named numpy.logical_and - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.greater - imported by numpy (top-level), scipy.optimize._minpack_py (top-level), scipy.signal._bsplines (top-level)
missing module named numpy.sign - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.conjugate - imported by numpy (top-level), scipy.linalg._matfuncs (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.logical_not - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.single - imported by numpy (top-level), scipy.linalg._decomp_schur (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.floor - imported by numpy (top-level), scipy.special._basic (top-level), scipy.special._orthogonal (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._bsplines (top-level)
missing module named numpy.arcsin - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level)
missing module named numpy.arccos - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level), scipy.special._orthogonal (top-level)
missing module named numpy.conj - imported by numpy (top-level), scipy.linalg._decomp (top-level), scipy.io._mmio (top-level)
missing module named numpy.inexact - imported by numpy (top-level), scipy.linalg._decomp (top-level), scipy.special._basic (top-level), scipy.optimize._minpack_py (top-level)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named pygraphviz - imported by networkx.drawing.nx_agraph (delayed, optional)
missing module named cupy_backends - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'cupy.cuda' - imported by thinc.model (delayed), spacy.compat (optional), scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'numpy.lib.array_utils' - imported by joblib._memmapping_reducer (delayed, optional), scipy._lib.array_api_compat.common._linalg (conditional), astropy.utils.shapes (conditional), astropy.stats.sigma_clipping (conditional), astropy.uncertainty.core (conditional)
missing module named 'cupy.linalg' - imported by scipy._lib.array_api_compat.cupy.linalg (top-level)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional, optional), google.protobuf.internal.api_implementation (conditional, optional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (conditional)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named 'tensorflow.compat' - imported by transformers.models.bert_generation.modeling_bert_generation (delayed, optional), transformers.models.deprecated.realm.retrieval_realm (delayed), keras.src.callbacks.tensorboard (delayed), tensorboard.util.op_evaluator (delayed), tensorboard.util.encoder (delayed), tensorboard.plugins.audio.summary (delayed), tensorboard.plugins.custom_scalar.summary (delayed), tensorboard.plugins.histogram.summary (delayed), tensorboard.plugins.image.summary (delayed), tensorboard.plugins.pr_curve.summary (delayed), tensorboard.plugins.scalar.summary (delayed), tensorboard.plugins.text.summary (delayed)
missing module named distributed - imported by fsspec.transaction (delayed), joblib._parallel_backends (delayed, optional), joblib._dask (optional)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed), fsspec.parquet (delayed), astropy.io.misc.parquet (delayed)
missing module named fastparquet - imported by fsspec.parquet (delayed)
missing module named urllib3_secure_extra - imported by urllib3 (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional), httpx._decoders (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional), httpx._decoders (optional)
runtime module named urllib3.packages.six.moves - imported by http.client (top-level), urllib3.util.response (top-level), urllib3.connectionpool (top-level), urllib3.packages.six.moves.urllib (top-level), urllib3.util.queue (top-level)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named collections.MutableMapping - imported by collections (optional), urllib3._collections (optional), html5lib.treebuilders.dom (optional), html5lib.treebuilders.etree_lxml (optional), srsly.ruamel_yaml.compat (conditional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional), urllib3._collections (optional), html5lib._utils (optional), html5lib._trie._base (optional), srsly.ruamel_yaml.compat (conditional), srsly.ruamel_yaml.comments (conditional), google.auth.jwt (optional), google.auth.pluggable (optional), google.auth.identity_pool (optional), sortedcontainers.sorteddict (optional)
missing module named 'urllib3.packages.six.moves.urllib.parse' - imported by urllib3.request (top-level), urllib3.poolmanager (top-level)
missing module named Queue - imported by urllib3.util.queue (conditional), gensim.models.callbacks (conditional), gensim.models.lda_worker (optional)
missing module named dummy_threading - imported by requests.cookies (optional), joblib.compressor (optional)
missing module named UserDict - imported by pytz.lazy (optional), simplejson.ordered_dict (top-level)
missing module named requests_kerberos - imported by fsspec.implementations.webhdfs (delayed, conditional)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named smbprotocol - imported by fsspec.implementations.smb (top-level)
missing module named smbclient - imported by fsspec.implementations.smb (top-level)
missing module named lexicon - imported by invoke.util (optional), invoke.parser.parser (optional), invoke.parser.context (optional)
missing module named fluidity - imported by invoke.parser.parser (optional)
missing module named gssapi - imported by paramiko.ssh_gss (optional)
missing module named kerchunk - imported by fsspec.implementations.reference (delayed)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named numba - imported by pandas.core._numba.executor (delayed, conditional), pandas.core.util.numba_ (delayed, conditional), pandas.core.groupby.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core._numba.extensions (top-level)
missing module named 'numba.extending' - imported by pandas.core._numba.kernels.sum_ (top-level)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays.arrow.accessors (conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.arrow.array (conditional)
missing module named 'numba.typed' - imported by pandas.core._numba.extensions (delayed)
missing module named 'numba.core' - imported by pandas.core._numba.extensions (top-level)
missing module named pyarrow - imported by pandas.core.arrays.arrow.accessors (conditional), pandas.core.arrays.masked (delayed), pandas.core.arrays.boolean (delayed, conditional), pandas.core.arrays.numeric (delayed, conditional), pandas.core.arrays.arrow._arrow_utils (top-level), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.arrays.interval (delayed), pandas.core.arrays.arrow.extension_types (top-level), pandas.core.arrays.period (delayed), pandas.core.interchange.utils (delayed, conditional), pandas.io.parsers.base_parser (delayed, conditional), pandas.core.methods.describe (delayed, conditional), pandas.io.sql (delayed, conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.strings.accessor (delayed, conditional), pandas.core.interchange.buffer (conditional), pandas.io.feather_format (delayed), pandas.core.indexes.base (delayed, conditional), pandas.core.dtypes.cast (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional), pandas.core.reshape.encoding (delayed, conditional), pandas._testing (conditional), astropy.io.misc.parquet (delayed)
missing module named scipy.stats.spearmanr - imported by scipy.stats (top-level), sklearn.isotonic (top-level), transformers.data.metrics (conditional), pandas.core.nanops (delayed, conditional), astropy.stats.tests.test_funcs (delayed)
missing module named sparse - imported by scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg.issymmetric - imported by scipy.linalg (top-level), scipy.optimize._optimize (top-level)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.linalg.bandwidth - imported by scipy.linalg (top-level), scipy.linalg._matfuncs (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog_backend (top-level), scipy.stats._multivariate (top-level)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named scipy.sparse.linalg.matrix_power - imported by scipy.sparse.linalg (delayed), scipy.sparse._matrix (delayed)
missing module named scikits - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.spatial.Voronoi - imported by scipy.spatial (top-level), scipy.stats._qmc (top-level)
missing module named scipy.special.rel_entr - imported by scipy.special (top-level), scipy.spatial.distance (top-level)
missing module named 'scikits.umfpack' - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named scipy.special.xlogy - imported by scipy.special (top-level), scipy.interpolate._rbf (top-level), sklearn._loss.loss (top-level), sklearn.metrics._classification (top-level), sklearn.metrics._regression (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named scipy.special.hyp2f1 - imported by scipy.special (conditional), astropy.cosmology.flrw.lambdacdm (conditional)
missing module named scipy.special.ellipkinc - imported by scipy.special (conditional), astropy.cosmology.flrw.lambdacdm (conditional)
missing module named scipy.special.j1 - imported by scipy.special (delayed, conditional), astropy.modeling.functional_models (delayed, conditional)
missing module named scipy.special.wofz - imported by scipy.special (delayed, conditional), astropy.modeling.functional_models (delayed, conditional)
missing module named scipy.special.betaincinv - imported by scipy.special (delayed, conditional), astropy.stats.funcs (delayed, conditional)
missing module named scipy.special.erfinv - imported by scipy.special (delayed, conditional), astropy.stats.funcs (delayed, conditional), astropy.stats.jackknife (delayed)
missing module named scipy.special.gammaincinv - imported by scipy.special (top-level), scipy.stats._qmvnt (top-level), astropy.modeling.functional_models (delayed, conditional)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.entr - imported by scipy.special (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.inv_boxcox - imported by scipy.special (top-level), sklearn.preprocessing._data (top-level)
missing module named scipy.special.boxcox - imported by scipy.special (top-level), sklearn.preprocessing._data (top-level)
missing module named scipy.special.sph_jn - imported by scipy.special (delayed, conditional, optional), sympy.functions.special.bessel (delayed, conditional, optional)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named cupyx - imported by scipy.special._support_alternative_backends (delayed, conditional), thinc.compat (optional)
missing module named pygments.formatters.LatexFormatter - imported by pygments.formatters (top-level), sphinx.highlighting (top-level)
missing module named pygments.formatters.HtmlFormatter - imported by pygments.formatters (top-level), sphinx.highlighting (top-level)
missing module named pygments.formatters.Terminal256Formatter - imported by pygments.formatters (conditional), transformers.agents.agents (conditional)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named pygments.lexers.TextLexer - imported by pygments.lexers (top-level), sphinx.highlighting (top-level)
missing module named pygments.lexers.RstLexer - imported by pygments.lexers (top-level), sphinx.highlighting (top-level)
missing module named pygments.lexers.PythonConsoleLexer - imported by pygments.lexers (top-level), sphinx.highlighting (top-level), sphinx.transforms.post_transforms.code (top-level)
missing module named pygments.lexers.CLexer - imported by pygments.lexers (top-level), sphinx.highlighting (top-level)
missing module named pygments.lexers.PythonLexer - imported by pygments.lexers (conditional), transformers.agents.agents (conditional), sphinx.highlighting (top-level)
missing module named Image - imported by docutils.parsers.rst.directives.images (optional)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named _typeshed - imported by pydantic._internal._dataclasses (conditional), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), anyio._core._tempfile (conditional), torch.utils._backport_slots (conditional), babel.messages.mofile (conditional), babel.messages.pofile (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), trio._file_io (conditional), trio._path (conditional), anyio._backends._trio (conditional), httpx._transports.wsgi (conditional)
missing module named Stemmer - imported by snowballstemmer (optional)
missing module named roman - imported by docutils.writers.latex2e (optional), docutils.writers.manpage (optional)
missing module named Levenshtein - imported by transformers.models.nougat.tokenization_nougat_fast (conditional), sphinx.versioning (optional)
missing module named sphinx.util.progress_message - imported by sphinx.util (conditional), sphinxcontrib.applehelp (conditional), sphinxcontrib.htmlhelp (conditional)
missing module named sphinx.util.SkipProgressMessage - imported by sphinx.util (conditional), sphinxcontrib.applehelp (conditional)
missing module named subunit - imported by twisted.trial.reporter (optional)
missing module named tomli - imported by _pytest.config.findpaths (delayed, conditional), incremental (delayed, conditional)
missing module named annotationlib - imported by attr._compat (conditional)
missing module named exceptiongroup - imported by _pytest.runner (conditional), _pytest.fixtures (conditional), _pytest._code.code (conditional), anyio._core._exceptions (conditional), anyio._core._sockets (conditional), starlette._utils (conditional, optional), anyio._backends._asyncio (conditional), trio._core._run (conditional), trio.testing._check_streams (conditional), trio.testing._raises_group (conditional), trio._highlevel_open_tcp_listeners (conditional), trio._highlevel_open_tcp_stream (conditional), anyio._backends._trio (conditional), _pytest.unittest (conditional)
missing module named argcomplete - imported by _pytest._argcomplete (conditional, optional)
missing module named pexpect - imported by _pytest.pytester (conditional), _pytest.legacypath (conditional)
missing module named jieba - imported by transformers.models.cpm.tokenization_cpm (delayed, optional), transformers.models.cpm.tokenization_cpm_fast (delayed, optional), transformers.models.cpmant.tokenization_cpmant (conditional), transformers.models.xlm.tokenization_xlm (delayed, conditional, optional), spacy.lang.zh (delayed, optional), sphinx.search.zh (optional)
missing module named janome - imported by sphinx.search.ja (optional)
missing module named MeCab - imported by sphinx.search.ja (optional)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named scipy.stats.pearsonr - imported by scipy.stats (conditional), transformers.data.metrics (conditional)
missing module named scipy.stats.fisher_exact - imported by scipy.stats (optional), nltk.metrics.association (optional)
missing module named scipy.stats.norm - imported by scipy.stats (optional), nltk.translate.gale_church (optional), scipy.stats._survival (top-level), astropy.stats.circstats (delayed), astropy.uncertainty.tests.test_distribution (conditional)
missing module named scipy.stats.chi2 - imported by scipy.stats (top-level), sklearn.covariance._robust_covariance (top-level), astropy.stats.tests.test_funcs (delayed)
missing module named scipy.stats.gmean - imported by scipy.stats (top-level), sklearn._loss.link (top-level), scipy.stats.mstats (top-level)
missing module named scipy.stats.rankdata - imported by scipy.stats (top-level), sklearn.model_selection._search (top-level), sklearn.metrics._ranking (top-level)
missing module named scipy.stats.truncnorm - imported by scipy.stats (delayed, conditional), transformers.models.esm.modeling_esmfold (delayed, conditional)
missing module named traitlets - imported by pandas.io.formats.printing (delayed, conditional)
missing module named 'IPython.core' - imported by sympy.interactive.printing (delayed, optional), pandas.io.formats.printing (delayed, conditional), rich.pretty (delayed, optional), dotenv.ipython (top-level), h5py (delayed, conditional, optional), h5py.ipy_completer (top-level), spacy.displacy (delayed, conditional), memory_profiler (delayed, conditional, optional)
missing module named IPython - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional, optional), pandas.io.formats.printing (delayed), h5py (delayed, conditional, optional), h5py.ipy_completer (top-level), keras.src.utils.model_visualization (delayed, conditional, optional), keras.src.saving.file_editor (delayed, optional), astropy.logger (delayed, conditional), memory_profiler (delayed), tensorflow.python.keras.utils.vis_utils (delayed, conditional, optional), astropy.table.pandas (optional)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named cchardet - imported by bs4.dammit (optional)
missing module named html5lib.treebuilders._base - imported by html5lib.treebuilders (top-level), lxml.html._html5builder (top-level)
missing module named html5lib.XHTMLParser - imported by html5lib (optional), lxml.html.html5parser (optional)
missing module named 'genshi.core' - imported by html5lib.treewalkers.genshi (top-level)
missing module named genshi - imported by html5lib.treewalkers.genshi (top-level)
missing module named urlparse - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named urllib2 - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named lxml_html_clean - imported by lxml.html.clean (optional)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named pysqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed)
missing module named sqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, optional)
missing module named 'psycopg.pq' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed)
missing module named 'psycopg.types' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named 'psycopg.adapt' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named psycopg - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named asyncpg - imported by sqlalchemy.dialects.postgresql.asyncpg (delayed)
missing module named oracledb - imported by sqlalchemy.dialects.oracle.oracledb (delayed, conditional)
missing module named cx_Oracle - imported by sqlalchemy.dialects.oracle.cx_oracle (delayed)
missing module named 'opentelemetry.semconv' - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named 'opentelemetry.sdk' - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named opentelemetry - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.context_propagation (conditional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named 'opentelemetry.trace' - imported by mysql.connector.opentelemetry.context_propagation (conditional)
missing module named wmi - imported by dns.win32util (conditional)
missing module named curio - imported by sniffio._impl (delayed, conditional)
missing module named 'hypothesis.internal' - imported by trio._core._run (delayed, optional)
missing module named hypothesis - imported by torch.testing._internal.common_utils (optional), trio._core._run (delayed), spacy.tests.conftest (top-level), spacy.tests.parser.test_nn_beam (top-level), spacy.tests.pipeline.test_edit_tree_lemmatizer (top-level), spacy.tests.tokenizer.test_explain (top-level), thinc.tests.backends.test_ops (top-level), thinc.tests.conftest (top-level), thinc.tests.layers.test_linear (top-level), thinc.tests.layers.test_uniqued (top-level), thinc.tests.shims.test_pytorch_grad_scaler (top-level), thinc.tests.test_util (top-level), torch.testing._internal.hypothesis_utils (top-level), astropy.io.fits.hdu.compressed.tests.test_compressed (top-level), astropy.table.tests.test_groups (top-level), astropy.time.tests.test_precision (top-level), astropy.utils.tests.test_shapes (top-level)
missing module named collections.ValuesView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.KeysView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.ItemsView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.Set - imported by collections (conditional), srsly.ruamel_yaml.comments (conditional), sortedcontainers.sortedset (optional)
missing module named collections.MutableSequence - imported by collections (conditional), srsly.ruamel_yaml.compat (conditional), sortedcontainers.sortedlist (optional)
missing module named uvloop - imported by aiohttp.worker (delayed), anyio._backends._asyncio (delayed, conditional)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'h2.settings' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.exceptions' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.events' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.connection' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named h2 - imported by httpcore._sync.http2 (top-level), httpx._client (delayed, conditional, optional)
missing module named 'h2.config' - imported by httpcore._async.http2 (top-level)
missing module named 'IPython.display' - imported by tqdm.notebook (conditional, optional), rich.jupyter (delayed, optional), rich.live (delayed, conditional, optional), huggingface_hub._login (delayed, optional), transformers.utils.notebook (top-level), transformers.agents.agent_types (delayed), moviepy.video.io.display_in_notebook (optional), astropy.table.notebook_backends (delayed), astropy.utils.console (delayed, conditional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional), rich.live (delayed, conditional, optional), astropy.utils.console (delayed, conditional)
missing module named linkify_it - imported by markdown_it.main (optional)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional)
missing module named zstandard - imported by fsspec.compression (optional), smart_open.compression (delayed), httpx._decoders (optional)
missing module named 'aioquic.quic' - imported by dns.quic._asyncio (top-level), dns.quic._common (top-level), dns.quic._sync (top-level), dns.quic._trio (top-level)
missing module named 'aioquic.h3' - imported by dns.quic._common (top-level)
missing module named aioquic - imported by dns.quic (conditional)
missing module named asyncmy - imported by sqlalchemy.dialects.mysql.asyncmy (delayed)
missing module named pymysql - imported by sqlalchemy.dialects.mysql.aiomysql (delayed)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named 'pyarrow.fs' - imported by pandas.io.orc (conditional), fsspec.implementations.arrow (delayed)
missing module named 'pyu2f.model' - imported by google.oauth2.challenges (delayed, optional)
missing module named 'pyu2f.errors' - imported by google.oauth2.challenges (delayed, optional)
missing module named pyu2f - imported by google.oauth2.challenges (delayed, optional)
missing module named 'requests.packages.urllib3' - imported by google.auth.transport.requests (top-level)
missing module named 'google.appengine' - imported by google.auth.app_engine (optional)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named ujson - imported by fastapi.responses (optional), fsspec.implementations.cache_metadata (optional), fsspec.implementations.reference (optional)
missing module named 'libarchive.ffi' - imported by fsspec.implementations.libarchive (top-level)
missing module named libarchive - imported by fsspec.implementations.libarchive (top-level)
missing module named js - imported by fsspec.implementations.http_sync (delayed, optional)
missing module named async_timeout - imported by aiohttp.helpers (conditional), aiohttp.web_ws (conditional), aiohttp.client_ws (conditional)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named aiodns - imported by aiohttp.resolver (optional)
missing module named pygit2 - imported by fsspec.implementations.git (top-level)
missing module named 'distributed.worker' - imported by fsspec.implementations.dask (top-level)
missing module named 'distributed.client' - imported by fsspec.implementations.dask (top-level)
missing module named dask - imported by joblib._dask (optional), fsspec.implementations.dask (top-level), astropy.modeling._fitting_parallel (delayed, optional), astropy.modeling.tests.test_fitting_parallel (top-level), astropy.units.tests.test_units (delayed)
missing module named panel - imported by fsspec.gui (top-level)
missing module named fuse - imported by fsspec.fuse (top-level)
missing module named lz4 - imported by fsspec.compression (optional), joblib.compressor (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named awscrt - imported by botocore.compat (optional), botocore.useragent (delayed, optional), botocore.httpchecksum (conditional)
missing module named 'botocore.customizations' - imported by botocore.useragent (optional), botocore.configprovider (optional)
missing module named 'awscrt.io' - imported by s3transfer.crt (top-level)
missing module named 'awscrt.auth' - imported by s3transfer.crt (top-level)
missing module named 'awscrt.http' - imported by s3transfer.crt (top-level)
missing module named 'awscrt.s3' - imported by boto3.s3.transfer (conditional), s3transfer.crt (top-level)
missing module named array_api_compat - imported by sklearn.utils._array_api (delayed, conditional, optional), sklearn.utils._testing (delayed, optional)
missing module named 'numpydoc.docscrape' - imported by sklearn.utils._testing (delayed)
missing module named numpydoc - imported by sklearn.utils._testing (delayed, optional)
missing module named viztracer - imported by joblib.externals.loky.initializers (delayed, optional)
missing module named 'distributed.utils' - imported by joblib._dask (conditional, optional)
missing module named 'dask.distributed' - imported by joblib._dask (conditional)
missing module named 'dask.sizeof' - imported by joblib._dask (conditional)
missing module named 'dask.utils' - imported by joblib._dask (conditional), astropy.io.fits.hdu.image (delayed, conditional)
missing module named 'lz4.frame' - imported by joblib.compressor (optional)
missing module named pyamg - imported by sklearn.manifold._spectral_embedding (delayed, conditional, optional)
missing module named keras.src.ops.convert_to_tensor - imported by keras.src.ops (top-level), keras.src.utils.torch_utils (top-level)
missing module named keras.src.ops.convert_to_numpy - imported by keras.src.ops (top-level), keras.src.utils.torch_utils (top-level)
missing module named pydotplus - imported by keras.src.utils.model_visualization (optional), tensorflow.python.keras.utils.vis_utils (optional)
missing module named pydot_ng - imported by keras.src.utils.model_visualization (optional), tensorflow.python.keras.utils.vis_utils (optional)
missing module named 'flax.core' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), keras.src.utils.jax_layer (delayed)
missing module named 'jax.typing' - imported by optree.integration.jax (top-level)
missing module named 'jax._src' - imported by optree.integration.jax (top-level), keras.src.backend.jax.nn (delayed, optional)
missing module named jax - imported by optree.integration.jax (top-level), opt_einsum.backends.jax (delayed, conditional), keras.src.trainers.data_adapters.data_adapter_utils (delayed), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), safetensors.flax (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.generation.flax_logits_process (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), transformers.generation.flax_utils (top-level), keras.src.backend.jax.core (top-level), keras.src.backend.jax.distribution_lib (top-level), keras.src.backend.jax.image (top-level), keras.src.backend.jax.linalg (top-level), keras.src.backend.jax.math (top-level), keras.src.backend.jax.nn (top-level), keras.src.backend.jax.random (top-level), keras.src.backend.jax.rnn (top-level), keras.src.backend.jax.trainer (top-level), keras.src.backend.numpy.image (top-level), keras.src.backend.numpy.nn (top-level), keras.src.backend.jax.export (delayed), keras.src.backend.jax.optimizer (top-level), tensorflow.lite.python.util (optional)
missing module named 'jax.experimental' - imported by keras.src.trainers.data_adapters.data_adapter_utils (delayed), transformers.generation.flax_logits_process (top-level), keras.src.testing.test_case (delayed, conditional), keras.src.backend.jax.core (top-level), keras.src.backend.jax.numpy (top-level), keras.src.backend.jax.nn (top-level), keras.src.backend.jax.sparse (top-level), keras.src.backend.jax.export (delayed, conditional)
missing module named 'jax.nn' - imported by keras.src.backend.jax.nn (delayed, optional)
missing module named 'jax.scipy' - imported by keras.src.backend.jax.linalg (top-level)
missing module named 'jax.numpy' - imported by transformers.feature_extraction_utils (delayed, conditional), transformers.image_transforms (conditional), transformers.tokenization_utils_base (delayed, conditional), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_outputs (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), safetensors.flax (top-level), transformers.utils.generic (delayed, conditional), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.conditional_detr.image_processing_conditional_detr (delayed, conditional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, conditional), transformers.models.detr.image_processing_detr (delayed, conditional), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, conditional), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.mt5.modeling_flax_mt5 (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.owlv2.processing_owlv2 (delayed, conditional), transformers.models.owlvit.processing_owlvit (delayed, conditional), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.rt_detr.image_processing_rt_detr (delayed, conditional), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.wav2vec2.tokenization_wav2vec2 (conditional), transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (conditional), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.generation.flax_logits_process (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), transformers.models.yolos.image_processing_yolos (delayed, conditional), transformers.generation.flax_utils (top-level), transformers.models.deprecated.deta.image_processing_deta (delayed, conditional), transformers.models.deprecated.jukebox.tokenization_jukebox (delayed, conditional), keras.src.backend.jax.core (top-level), keras.src.backend.jax.image (top-level), keras.src.backend.jax.linalg (top-level), keras.src.backend.jax.math (top-level), keras.src.backend.jax.numpy (top-level), keras.src.backend.jax.nn (top-level), keras.src.backend.jax.sparse (top-level)
missing module named keras.src.backend.random - imported by keras.src.backend (top-level), keras.src.ops (top-level), keras.src.testing.test_case (delayed), keras.src.initializers.random_initializers (top-level)
missing module named pydantic.ConstrainedStr - imported by pydantic (optional), spacy.schemas (optional)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named cython - imported by pydantic.v1.version (optional)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional), fastapi.openapi.models (optional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional), fastapi._compat (conditional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic.deprecated.copy_internals (delayed, conditional), huggingface_hub._webhooks_payload (conditional), fastapi.exceptions (top-level), fastapi.types (top-level), fastapi._compat (top-level), fastapi.openapi.models (top-level), fastapi.security.http (top-level), fastapi.utils (top-level), fastapi.dependencies.utils (top-level), fastapi.encoders (top-level), fastapi.routing (top-level), confection (optional), spacy.schemas (optional), spacy.pipeline._edit_tree_internals.schemas (optional), weasel.schemas (optional), thinc.tests.test_config (optional)
missing module named 'google.colab' - imported by huggingface_hub.utils._auth (delayed, optional), spacy.util (delayed, optional)
missing module named 'torch_xla.core' - imported by huggingface_hub.serialization._torch (delayed, conditional, optional), transformers.utils.import_utils (delayed, conditional, optional), transformers.trainer_utils (delayed, conditional), transformers.trainer_pt_utils (delayed, conditional), transformers.training_args (conditional), torch._dynamo.testing (delayed, conditional), transformers.trainer (conditional), torch._dynamo.backends.torchxla (delayed, optional)
missing module named torch_xla - imported by huggingface_hub.serialization._torch (delayed, conditional), transformers.pytorch_utils (delayed, conditional), transformers.utils.import_utils (delayed), transformers.trainer (conditional), torch._tensor (delayed, conditional)
missing module named hf_transfer - imported by huggingface_hub.file_download (delayed, conditional, optional), huggingface_hub.lfs (delayed, optional)
missing module named tf_keras - imported by tensorflow.python.util.lazy_loader (delayed, conditional, optional), huggingface_hub.keras_mixin (conditional, optional), transformers.activations_tf (optional), transformers.modeling_tf_utils (optional), tensorflow.python.saved_model.load (delayed, conditional, optional)
missing module named fastai - imported by huggingface_hub.fastai_utils (delayed)
missing module named toml - imported by huggingface_hub.fastai_utils (delayed, optional)
missing module named orjson - imported by fastapi.responses (optional)
missing module named 'multipart.multipart' - imported by starlette.formparsers (conditional, optional), starlette.requests (conditional, optional), fastapi.dependencies.utils (delayed, optional)
missing module named 'python_multipart.multipart' - imported by starlette.formparsers (conditional, optional), starlette.requests (conditional, optional)
missing module named multipart - imported by starlette.formparsers (conditional, optional), fastapi.dependencies.utils (delayed, optional)
missing module named python_multipart - imported by starlette.formparsers (conditional, optional), fastapi.dependencies.utils (delayed, optional)
missing module named gradio - imported by huggingface_hub._webhooks_server (delayed, conditional), transformers.agents.tools (delayed, optional), transformers.agents.monitoring (delayed, optional)
missing module named tensorboardX - imported by huggingface_hub._tensorboard_logger (conditional, optional), transformers.integrations.integration_utils (delayed, conditional, optional)
missing module named 'ipywidgets.widgets' - imported by huggingface_hub._login (delayed, optional)
missing module named 'InquirerPy.separator' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named 'InquirerPy.base' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named InquirerPy - imported by huggingface_hub.commands.delete_cache (optional)
missing module named 'IPython.utils' - imported by h5py.ipy_completer (top-level), astropy.utils.console (delayed, conditional), memory_profiler (delayed, conditional)
missing module named mpi4py - imported by h5py._hl.files (delayed)
missing module named keras.src.backend.random_seed_dtype - imported by keras.src.backend (delayed), keras.src.random.seed_generator (delayed)
missing module named keras.src.backend.convert_to_tensor - imported by keras.src.backend (delayed), keras.src.random.seed_generator (delayed)
missing module named keras.src.backend.is_tensor - imported by keras.src.backend (top-level), keras.src.ops (top-level)
missing module named keras.src.backend.cond - imported by keras.src.backend (top-level), keras.src.ops (top-level)
missing module named keras.src.backend.cast - imported by keras.src.backend (top-level), keras.src.ops (top-level)
missing module named 'openvino.runtime' - imported by keras.src.backend.openvino.core (top-level), keras.src.backend.openvino.math (top-level), keras.src.backend.openvino.nn (top-level), keras.src.backend.openvino.numpy (top-level), keras.src.backend.openvino.random (top-level), keras.src.backend.openvino.trainer (top-level)
missing module named openvino - imported by keras.src.backend.openvino.core (top-level), keras.src.backend.openvino.math (top-level), keras.src.backend.openvino.nn (top-level), keras.src.backend.openvino.numpy (top-level), keras.src.backend.openvino.random (top-level), keras.src.backend.openvino.trainer (top-level)
missing module named onnx - imported by torch.onnx._internal._lazy_import (conditional), torch.onnx._internal.exporter._schemas (top-level), torch.onnx._internal.exporter._building (conditional), transformers.onnx.convert (delayed), torch.onnx._internal.onnx_proto_utils (delayed, optional), keras.src.export.tf2onnx_lib (delayed), torch.onnx._internal.fx.serialization (delayed, conditional), torch.onnx._internal.fx.type_utils (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (delayed), torch.onnx._internal.onnxruntime (conditional), torch.onnx._internal._exporter_legacy (delayed, optional), torch.utils.tensorboard._onnx_graph (delayed), torch.onnx.verification (delayed, optional)
missing module named 'tensorflow.saved_model' - imported by keras.src.export.saved_model (delayed)
missing module named 'tensorflow.summary' - imported by keras.src.callbacks.tensorboard (delayed, conditional)
missing module named 'tensorflow.experimental' - imported by thinc.compat (delayed), transformers.models.sam.image_processing_sam (conditional), keras.src.backend.tensorflow.distribution_lib (top-level)
missing module named oauth2client - imported by tensorflow.python.distribute.cluster_resolver.gce_cluster_resolver (optional), tensorflow.python.tpu.client.client (optional)
missing module named googleapiclient - imported by tensorflow.python.distribute.cluster_resolver.gce_cluster_resolver (optional), tensorflow.python.tpu.client.client (optional)
missing module named 'tensorflow.contrib' - imported by tensorflow.python.tools.import_pb_to_tensorboard (optional)
missing module named 'keras.optimizers' - imported by tensorflow.python.saved_model.load (delayed, conditional, optional)
missing module named 'tf_keras.optimizers' - imported by transformers.optimization_tf (optional), tensorflow.python.saved_model.load (delayed, conditional, optional)
missing module named rules_python - imported by tensorflow.python.platform.resource_loader (optional)
missing module named tensorflow.python.keras.layers.wrappers - imported by tensorflow.python.keras.layers (delayed), tensorflow.python.keras.utils.vis_utils (delayed)
missing module named 'six.moves.urllib.request' - imported by tensorflow.python.keras.utils.data_utils (top-level)
missing module named tensorflow.python.keras.__version__ - imported by tensorflow.python.keras (delayed), tensorflow.python.keras.saving.saving_utils (delayed), tensorflow.python.keras.saving.hdf5_format (delayed), tensorflow.python.keras.engine.training (delayed)
missing module named portpicker - imported by tensorflow.python.framework.test_util (delayed), tensorflow.dtensor.python.tests.multi_client_test_util (top-level), tensorflow.python.debug.lib.grpc_debug_test_server (top-level)
missing module named 'tensorflow.python.framework.is_mlir_bridge_test_true' - imported by tensorflow.python.framework.test_util (optional)
missing module named 'tensorflow.python.framework.is_mlir_bridge_test_false' - imported by tensorflow.python.framework.test_util (optional)
missing module named 'tensorflow.python.framework.is_xla_test_true' - imported by tensorflow.python.framework.test_util (optional)
missing module named 'IPython.ipapi' - imported by memory_profiler (delayed, conditional)
missing module named 'IPython.ipstruct' - imported by memory_profiler (delayed, conditional)
missing module named 'IPython.genutils' - imported by memory_profiler (delayed, conditional)
missing module named objgraph - imported by torch.testing._internal.common_utils (delayed, conditional, optional), tensorflow.python.distribute.test_util (optional), astropy.io.fits.tests.test_table (optional)
missing module named tblib - imported by tensorflow.python.distribute.multi_process_runner (optional)
missing module named dill - imported by torch.utils._import_utils (delayed), torch.utils.data.graph (delayed, conditional, optional), tensorflow.python.distribute.multi_process_runner (optional)
missing module named cloud_tpu_client - imported by tensorflow.python.distribute.cluster_resolver.tpu.tpu_cluster_resolver (optional)
missing module named kubernetes - imported by tensorflow.python.distribute.cluster_resolver.kubernetes_cluster_resolver (delayed, conditional, optional)
missing module named grpc_reflection - imported by grpc (optional)
missing module named grpc_health - imported by grpc (optional)
missing module named grpc_tools - imported by grpc._runtime_protos (delayed, optional), grpc (optional)
missing module named 'grpc_tools.protoc' - imported by grpc._runtime_protos (delayed, conditional)
missing module named astn - imported by gast.ast2 (top-level)
missing module named tflite_runtime - imported by tensorflow.lite.python.metrics.metrics (conditional), tensorflow.lite.python.interpreter (conditional), tensorflow.lite.python.analyzer (conditional), tensorflow.lite.tools.visualize (conditional)
missing module named theano - imported by opt_einsum.backends.theano (delayed)
missing module named tensorflow.python.framework.fast_tensor_util - imported by tensorflow.python.framework (optional), tensorflow.python.framework.tensor_util (optional)
missing module named tensorboard.compat.notf - imported by tensorboard.compat (delayed, optional)
missing module named moviepy.editor - imported by moviepy (delayed, optional), torch.utils.tensorboard.summary (delayed, optional)
missing module named tifffile - imported by imageio.plugins.tifffile (optional), imageio.plugins.tifffile_v3 (top-level)
missing module named SimpleITK - imported by imageio.plugins.simpleitk (delayed, optional)
missing module named itk - imported by imageio.plugins.simpleitk (delayed, optional)
missing module named rawpy - imported by imageio.plugins.rawpy (top-level)
missing module named 'av.codec' - imported by imageio.plugins.pyav (top-level)
missing module named 'av.filter' - imported by imageio.plugins.pyav (top-level)
missing module named av - imported by transformers.image_utils (conditional), transformers.pipelines.video_classification (conditional), imageio.plugins.pyav (top-level)
missing module named pillow_heif - imported by imageio.plugins.pillow (delayed, optional)
missing module named osgeo - imported by imageio.plugins.gdal (delayed, optional)
missing module named astropy.units.nmgy - imported by astropy.units (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.mgy - imported by astropy.units (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.erg - imported by astropy.units (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.STflux - imported by astropy.units (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.Jy - imported by astropy.units (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.ABflux - imported by astropy.units (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.AA - imported by astropy.units (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.deg - imported by astropy.units (top-level), astropy.table.tests.test_pickle (top-level)
missing module named 'numpy._core.arrayprint' - imported by astropy.units.quantity_helper.function_helpers (delayed, conditional), astropy.utils.masked.function_helpers (delayed, conditional)
missing module named astropy.time.Time - imported by astropy.time (delayed), astropy.time.time_helper.function_helpers (delayed), astropy.io.misc.yaml (top-level), astropy.table.table (delayed, conditional), astropy.io.fits.connect (top-level), astropy.io.fits.fitstime (top-level), astropy.io.fits.convenience (delayed, conditional), astropy.visualization.time (top-level), astropy.coordinates.sky_coordinate (top-level), astropy.coordinates.builtin_frames.utils (top-level), astropy.utils.iers.iers (top-level), astropy.coordinates.earth_orientation (top-level), astropy.coordinates.erfa_astrom (top-level), astropy.coordinates.builtin_frames.lsr (top-level), astropy.coordinates.attributes (delayed), astropy.wcs.wcsapi.fitswcs (delayed), astropy.wcs.utils (delayed), astropy.table.index (delayed), astropy.coordinates.tests.accuracy.generate_spectralcoord_ref (conditional), astropy.coordinates.tests.accuracy.test_altaz_icrs (top-level), astropy.coordinates.tests.accuracy.test_ecliptic (top-level), astropy.coordinates.tests.accuracy.test_fk4_no_e_fk4 (top-level), astropy.coordinates.tests.accuracy.test_fk4_no_e_fk5 (top-level), astropy.coordinates.tests.accuracy.test_galactic_fk4 (top-level), astropy.coordinates.tests.accuracy.test_icrs_fk5 (top-level), astropy.coordinates.tests.test_arrays (top-level), astropy.coordinates.tests.test_atc_replacements (top-level), astropy.coordinates.tests.test_celestial_transformations (top-level), astropy.coordinates.tests.test_earth (top-level), astropy.coordinates.tests.test_earth_orientation (top-level), astropy.coordinates.tests.test_erfa_astrom (top-level), astropy.coordinates.tests.test_finite_difference_velocities (top-level), astropy.coordinates.tests.test_frames (top-level), astropy.coordinates.tests.test_funcs (top-level), astropy.coordinates.tests.test_iau_fullstack (top-level), astropy.coordinates.tests.test_icrs_observed_transformations (top-level), astropy.coordinates.tests.test_intermediate_transformations (top-level), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_shape_manipulation (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.coordinates.tests.test_skyoffset_transformations (top-level), astropy.coordinates.tests.test_solar_system (top-level), astropy.coordinates.tests.test_transformations (top-level), astropy.coordinates.tests.test_utils (top-level), astropy.coordinates.tests.test_velocity_corrs (top-level), astropy.io.ascii.tests.test_cds (top-level), astropy.io.ascii.tests.test_ecsv (top-level), astropy.io.fits.tests.test_connect (top-level), astropy.io.fits.tests.test_fitstime (top-level), astropy.io.misc.tests.test_parquet (top-level), astropy.io.misc.tests.test_yaml (top-level), astropy.table.tests.test_groups (top-level), astropy.table.tests.test_index (top-level), astropy.table.tests.test_table (top-level), astropy.table.tests.test_jsviewer (top-level), astropy.table.tests.test_masked (top-level), astropy.table.tests.test_operations (top-level), astropy.timeseries.io.kepler (top-level), astropy.timeseries.sampled (top-level), astropy.timeseries.binned (top-level), astropy.timeseries.downsample (top-level), astropy.timeseries.periodograms.bls.core (top-level), astropy.timeseries.periodograms.lombscargle.core (top-level), astropy.timeseries.periodograms.lombscargle_multiband.core (top-level), astropy.table.tests.test_pickle (top-level), astropy.time.tests.test_basic (top-level), astropy.time.tests.test_comparisons (top-level), astropy.time.tests.test_corrs (top-level), astropy.time.tests.test_custom_formats (top-level), astropy.time.tests.test_delta (top-level), astropy.time.tests.test_fast_parser (top-level), astropy.time.tests.test_functions (top-level), astropy.time.tests.test_guess (top-level), astropy.time.tests.test_mask (top-level), astropy.time.tests.test_methods (top-level), astropy.time.tests.test_pickle (top-level), astropy.time.tests.test_precision (top-level), astropy.time.tests.test_quantity_interaction (top-level), astropy.time.tests.test_sidereal (top-level), astropy.time.tests.test_update_leap_seconds (top-level), astropy.time.tests.test_ut1 (top-level), astropy.timeseries.periodograms.bls.tests.test_bls (top-level), astropy.timeseries.periodograms.lombscargle.tests.test_lombscargle (top-level), astropy.timeseries.periodograms.lombscargle_multiband.tests.test_lombscargle_multiband (top-level), astropy.timeseries.tests.test_binned (top-level), astropy.timeseries.tests.test_common (top-level), astropy.timeseries.tests.test_downsample (top-level), astropy.timeseries.tests.test_sampled (top-level), astropy.utils.iers.tests.test_iers (top-level), astropy.utils.iers.tests.test_leap_second (top-level), astropy.utils.masked.tests.test_containers (top-level), astropy.utils.tests.test_data_info (top-level), astropy.visualization.tests.test_time (top-level), astropy.visualization.wcsaxes.tests.test_display_world_coordinates (top-level), astropy.visualization.wcsaxes.tests.test_wcsapi (top-level), astropy.wcs.tests.test_utils (top-level), astropy.wcs.wcsapi.tests.test_fitswcs (top-level), astropy.wcs.wcsapi.wrappers.tests.test_sliced_wcs (top-level)
missing module named astropy.time.TimeDelta - imported by astropy.time (top-level), astropy.io.misc.yaml (top-level), astropy.table.table (delayed, conditional), astropy.io.fits.fitstime (top-level), astropy.utils.iers.iers (top-level), astropy.wcs.wcsapi.fitswcs (delayed), astropy.io.fits.tests.test_fitstime (top-level), astropy.io.misc.tests.test_parquet (top-level), astropy.table.tests.test_table (top-level), astropy.table.tests.test_operations (top-level), astropy.timeseries.io.kepler (top-level), astropy.timeseries.sampled (top-level), astropy.timeseries.binned (top-level), astropy.timeseries.downsample (top-level), astropy.timeseries.periodograms.bls.core (top-level), astropy.timeseries.periodograms.lombscargle.core (top-level), astropy.timeseries.periodograms.lombscargle_multiband.core (top-level), astropy.time.tests.test_basic (top-level), astropy.time.tests.test_comparisons (top-level), astropy.time.tests.test_corrs (top-level), astropy.time.tests.test_delta (top-level), astropy.time.tests.test_functions (top-level), astropy.time.tests.test_precision (top-level), astropy.time.tests.test_quantity_interaction (top-level), astropy.timeseries.periodograms.bls.tests.test_bls (top-level), astropy.timeseries.periodograms.lombscargle.tests.test_lombscargle (top-level), astropy.timeseries.periodograms.lombscargle_multiband.tests.test_lombscargle_multiband (top-level), astropy.timeseries.tests.test_binned (top-level), astropy.timeseries.tests.test_sampled (top-level), astropy.utils.iers.tests.test_iers (top-level), astropy.utils.iers.tests.test_leap_second (top-level)
missing module named astropy.time.TimeBase - imported by astropy.time (delayed), astropy.table.table (delayed)
missing module named astropy.cosmology.Planck13 - imported by astropy.cosmology (top-level), astropy.cosmology.tests.test_units (top-level)
missing module named astropy.cosmology.Planck18 - imported by astropy.cosmology (top-level), astropy.cosmology._io.tests.test_yaml (top-level), astropy.cosmology.flrw.tests.test_base (top-level), astropy.cosmology.flrw.tests.test_w0wacdm (top-level), astropy.cosmology.funcs.tests.test_comparison (top-level)
missing module named astropy.cosmology.WMAP5 - imported by astropy.cosmology (delayed), astropy.coordinates.tests.test_distance (delayed)
missing module named 'dask.array' - imported by astropy.io.fits.util (delayed), astropy.table.mixins.dask (top-level), astropy.nddata.nddata (delayed, conditional), astropy.modeling._fitting_parallel (delayed, optional), astropy.nddata.tests.test_nddata (delayed)
missing module named astropy.wcs.wcsapi.SlicedLowLevelWCS - imported by astropy.wcs.wcsapi (top-level), astropy.visualization.wcsaxes.wcsapi (top-level), astropy.nddata.nddata (top-level), astropy.nddata.mixins.ndslicing (top-level), astropy.nddata.tests.test_nddata (top-level), astropy.visualization.wcsaxes.tests.test_misc (top-level), astropy.visualization.wcsaxes.tests.test_wcsapi (top-level)
missing module named astropy.wcs.wcsapi.BaseLowLevelWCS - imported by astropy.wcs.wcsapi (top-level), astropy.visualization.wcsaxes.core (top-level), astropy.wcs.wcsapi.wrappers.base (top-level), astropy.nddata.nddata (top-level), astropy.nddata.mixins.ndslicing (top-level), astropy.modeling._fitting_parallel (top-level), astropy.visualization.wcsaxes.tests.test_wcsapi (top-level), astropy.wcs.wcsapi.conftest (top-level)
missing module named astropy.coordinates.frame_transform_graph - imported by astropy.coordinates (delayed), astropy.visualization.wcsaxes.utils (delayed), astropy.visualization.wcsaxes.transforms (top-level), astropy.coordinates.tests.test_icrs_observed_transformations (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.visualization.wcsaxes.tests.test_images (top-level)
missing module named astropy.coordinates.BaseCoordinateFrame - imported by astropy.coordinates (top-level), astropy.wcs.utils (top-level), astropy.visualization.wcsaxes.utils (top-level), astropy.visualization.wcsaxes.core (top-level), astropy.visualization.wcsaxes.transforms (top-level), astropy.visualization.wcsaxes.wcsapi (top-level), astropy.coordinates.errors (conditional), astropy.coordinates.tests.test_exceptions (conditional), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_separation (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.wcs.tests.test_utils (top-level)
missing module named bottleneck - imported by astropy.stats.nanfunctions (conditional)
missing module named 'sage.libs' - imported by mpmath.libmp.backend (conditional, optional), mpmath.libmp.libelefun (conditional, optional), mpmath.libmp.libmpf (conditional, optional), mpmath.libmp.libmpc (conditional, optional), mpmath.libmp.libhyper (delayed, conditional), mpmath.ctx_mp (conditional)
missing module named sage - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy2 - imported by mpmath.libmp.backend (conditional, optional), sympy.polys.domains.groundtypes (conditional), sympy.testing.runtests (delayed, conditional)
missing module named astropy.wcs.WCS - imported by astropy.wcs (top-level), astropy.visualization.wcsaxes.core (top-level), astropy.visualization.wcsaxes.wcsapi (top-level), astropy.nddata.ccddata (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.modeling.tests.test_fitting_parallel (top-level), astropy.nddata._testing (top-level), astropy.nddata.mixins.tests.test_ndarithmetic (top-level), astropy.nddata.tests.test_ccddata (top-level), astropy.nddata.tests.test_compat (top-level), astropy.nddata.tests.test_decorators (top-level), astropy.nddata.tests.test_nddata (top-level), astropy.nddata.tests.test_utils (top-level), astropy.visualization.wcsaxes.tests.test_coordinate_helpers (top-level), astropy.visualization.wcsaxes.tests.test_display_world_coordinates (top-level), astropy.visualization.wcsaxes.tests.test_images (top-level), astropy.visualization.wcsaxes.tests.test_frame (top-level), astropy.visualization.wcsaxes.tests.test_misc (top-level), astropy.visualization.wcsaxes.tests.test_transform_coord_meta (top-level), astropy.visualization.wcsaxes.tests.test_wcsapi (top-level), astropy.wcs.tests.test_auxprm (top-level), astropy.wcs.wcsapi.conftest (top-level), astropy.wcs.wcsapi.tests.test_high_level_api (top-level), astropy.wcs.wcsapi.tests.test_utils (top-level)
missing module named astropy.coordinates.angular_separation - imported by astropy.coordinates (top-level), astropy.visualization.wcsaxes.grid_paths (top-level), astropy.coordinates.tests.accuracy.test_fk4_no_e_fk4 (top-level), astropy.coordinates.tests.accuracy.test_fk4_no_e_fk5 (top-level), astropy.coordinates.tests.accuracy.test_galactic_fk4 (top-level), astropy.coordinates.tests.accuracy.test_icrs_fk5 (top-level), astropy.coordinates.tests.test_angular_separation (delayed), astropy.coordinates.tests.test_representation_arithmetic (top-level)
missing module named astropy.coordinates.Angle - imported by astropy.coordinates (top-level), astropy.visualization.wcsaxes.formatter_locator (top-level), astropy.coordinates.builtin_frames.galactic (top-level), astropy.coordinates.builtin_frames.galactocentric (top-level), astropy.coordinates.matching (top-level), astropy.coordinates.tests.accuracy.generate_spectralcoord_ref (conditional), astropy.coordinates.tests.accuracy.test_altaz_icrs (top-level), astropy.coordinates.tests.test_angles (top-level), astropy.coordinates.tests.test_angular_separation (top-level), astropy.coordinates.tests.test_api_ape5 (delayed), astropy.coordinates.tests.test_arrays (top-level), astropy.coordinates.tests.test_formatting (top-level), astropy.coordinates.tests.test_representation (top-level), astropy.coordinates.tests.test_matching (top-level), astropy.coordinates.tests.test_separation (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.coordinates.tests.test_skyoffset_transformations (top-level), astropy.io.misc.tests.test_parquet (top-level), astropy.io.misc.tests.test_yaml (top-level), astropy.modeling.tests.test_functional_models (top-level), astropy.stats.tests.test_sigma_clipping (top-level), astropy.uncertainty.tests.test_containers (top-level), astropy.uncertainty.tests.test_distribution (top-level), astropy.visualization.tests.test_units (top-level), astropy.visualization.wcsaxes.tests.test_utils (top-level)
missing module named astropy.coordinates.BaseRADecFrame - imported by astropy.coordinates (delayed), astropy.wcs.utils (delayed)
missing module named astropy.coordinates.Galactic - imported by astropy.coordinates (top-level), astropy.wcs.wcsapi.fitswcs (top-level), astropy.wcs.utils (delayed), astropy.coordinates.tests.test_arrays (top-level), astropy.coordinates.tests.test_exceptions (top-level), astropy.coordinates.tests.test_matching (top-level), astropy.coordinates.tests.test_separation (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.coordinates.tests.test_sky_coord_velocities (top-level), astropy.coordinates.tests.test_spectral_coordinate (top-level), astropy.visualization.wcsaxes.tests.test_utils (top-level), astropy.wcs.tests.test_utils (delayed), astropy.wcs.wcsapi.tests.test_fitswcs (top-level), astropy.wcs.wcsapi.wrappers.tests.test_sliced_wcs (top-level)
missing module named astropy.coordinates.FK4NoETerms - imported by astropy.coordinates (delayed), astropy.wcs.utils (delayed), astropy.coordinates.tests.test_regression (top-level), astropy.wcs.tests.test_utils (delayed)
missing module named astropy.coordinates.FK5 - imported by astropy.coordinates (delayed), astropy.wcs.utils (delayed), astropy.coordinates.tests.test_arrays (top-level), astropy.coordinates.tests.test_funcs (top-level), astropy.coordinates.tests.test_masked (top-level), astropy.coordinates.tests.test_separation (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.coordinates.tests.test_spectral_coordinate (top-level), astropy.visualization.wcsaxes.tests.test_display_world_coordinates (top-level), astropy.wcs.tests.test_utils (delayed), astropy.wcs.wcsapi.tests.test_fitswcs (top-level)
missing module named astropy.coordinates.FK4 - imported by astropy.coordinates (delayed), astropy.wcs.utils (delayed), astropy.coordinates.tests.test_arrays (top-level), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.wcs.tests.test_utils (delayed)
missing module named astropy.coordinates.StokesCoord - imported by astropy.coordinates (delayed), astropy.wcs.wcsapi.fitswcs (delayed), astropy.table.tests.test_operations (top-level), astropy.wcs.wcsapi.tests.test_fitswcs (top-level)
missing module named astropy.coordinates.Latitude - imported by astropy.coordinates (conditional), astropy.coordinates.baseframe (conditional), astropy.coordinates.tests.test_angles (top-level), astropy.coordinates.tests.test_api_ape5 (delayed), astropy.coordinates.tests.test_distance (top-level), astropy.coordinates.tests.test_earth (top-level), astropy.coordinates.tests.test_representation (top-level), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_representation_arithmetic (top-level), astropy.coordinates.tests.test_representation_methods (top-level), astropy.coordinates.tests.test_shape_manipulation (top-level), astropy.coordinates.tests.test_sites (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.coordinates.tests.test_unit_representation (top-level), astropy.io.misc.tests.test_parquet (top-level), astropy.io.misc.tests.test_yaml (top-level), astropy.uncertainty.tests.test_containers (top-level)
missing module named jplephem - imported by astropy.coordinates.solar_system (delayed)
missing module named astropy.coordinates.CartesianDifferential - imported by astropy.coordinates (top-level), astropy.coordinates.spectral_coordinate (top-level), astropy.coordinates.tests.test_celestial_transformations (top-level), astropy.coordinates.tests.test_finite_difference_velocities (top-level), astropy.coordinates.tests.test_intermediate_transformations (top-level), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_representation_arithmetic (top-level), astropy.coordinates.tests.test_sky_coord_velocities (top-level), astropy.coordinates.tests.test_spectral_coordinate (top-level), astropy.io.misc.tests.test_yaml (top-level), astropy.uncertainty.tests.test_containers (top-level)
missing module named astropy.coordinates.SpectralCoord - imported by astropy.coordinates (top-level), astropy.wcs.wcsapi.fitswcs (top-level), astropy.modeling.tests.test_bounding_box (top-level), astropy.wcs.wcsapi.tests.test_fitswcs (top-level)
missing module named astropy.constants.c - imported by astropy.constants (top-level), astropy.wcs.wcsapi.fitswcs (top-level), astropy.coordinates.spectral_coordinate (top-level), astropy.coordinates.sky_coordinate (top-level), astropy.coordinates.solar_system (top-level), astropy.coordinates.funcs (top-level), astropy.constants.tests.test_constant (delayed), astropy.constants.tests.test_prior_version (delayed), astropy.coordinates.tests.test_solar_system (top-level), astropy.coordinates.tests.test_spectral_coordinate (top-level), astropy.coordinates.tests.test_velocity_corrs (top-level)
missing module named astropy.coordinates.SphericalRepresentation - imported by astropy.coordinates (top-level), astropy.wcs.utils (top-level), astropy.coordinates.tests.test_atc_replacements (top-level), astropy.coordinates.tests.test_finite_difference_velocities (top-level), astropy.coordinates.tests.test_intermediate_transformations (top-level), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_representation_arithmetic (top-level), astropy.coordinates.tests.test_representation_methods (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.coordinates.tests.test_sky_coord_velocities (top-level), astropy.io.misc.tests.test_parquet (top-level), astropy.io.misc.tests.test_yaml (top-level), astropy.table.tests.test_operations (top-level), astropy.uncertainty.tests.test_containers (top-level), astropy.visualization.wcsaxes.tests.test_images (top-level)
missing module named astropy.coordinates.BaseGeodeticRepresentation - imported by astropy.coordinates (top-level), astropy.wcs.utils (top-level)
missing module named astropy.coordinates.BaseBodycentricRepresentation - imported by astropy.coordinates (top-level), astropy.wcs.utils (top-level)
missing module named astropy.coordinates.ITRS - imported by astropy.coordinates (top-level), astropy.wcs.utils (top-level), astropy.coordinates.tests.test_intermediate_transformations (top-level), astropy.coordinates.tests.test_regression (top-level), astropy.wcs.tests.test_utils (top-level), astropy.wcs.wcsapi.tests.test_fitswcs (top-level)
missing module named astropy.wcs.Sip - imported by astropy.wcs (top-level), astropy.nddata.utils (top-level), astropy.nddata.tests.test_utils (top-level)
missing module named astropy.coordinates.SkyCoord - imported by astropy.coordinates (delayed, conditional), astropy.wcs.wcsapi.high_level_api (delayed, conditional), astropy.visualization.wcsaxes.core (top-level), astropy.visualization.wcsaxes.transforms (top-level), astropy.visualization.wcsaxes.wcsapi (top-level), astropy.visualization.wcsaxes.patches (top-level), astropy.coordinates.spectral_coordinate (top-level), astropy.io.ascii.mrt (delayed), astropy.coordinates.jparser (top-level), astropy.coordinates.baseframe (conditional), astropy.wcs.wcsapi.fitswcs (delayed), astropy.wcs.utils (delayed), astropy.nddata.utils (top-level), astropy.coordinates.tests.accuracy.generate_spectralcoord_ref (conditional), astropy.coordinates.tests.accuracy.test_altaz_icrs (top-level), astropy.coordinates.tests.accuracy.test_ecliptic (top-level), astropy.coordinates.tests.test_arrays (top-level), astropy.coordinates.tests.test_celestial_transformations (top-level), astropy.coordinates.tests.test_erfa_astrom (top-level), astropy.coordinates.tests.test_frames (top-level), astropy.coordinates.tests.test_funcs (top-level), astropy.coordinates.tests.test_iau_fullstack (top-level), astropy.coordinates.tests.test_icrs_observed_transformations (top-level), astropy.coordinates.tests.test_intermediate_transformations (top-level), astropy.coordinates.tests.test_masked (top-level), astropy.coordinates.tests.test_matching (top-level), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_separation (top-level), astropy.coordinates.tests.test_shape_manipulation (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.coordinates.tests.test_sky_coord_velocities (top-level), astropy.coordinates.tests.test_skyoffset_transformations (top-level), astropy.coordinates.tests.test_spectral_coordinate (top-level), astropy.coordinates.tests.test_velocity_corrs (top-level), astropy.io.ascii.tests.test_cds (top-level), astropy.io.misc.tests.test_pandas (top-level), astropy.io.misc.tests.test_parquet (top-level), astropy.io.misc.tests.test_yaml (top-level), astropy.nddata.tests.test_utils (top-level), astropy.table.tests.test_table (top-level), astropy.table.tests.test_jsviewer (top-level), astropy.table.tests.test_mixin (top-level), astropy.table.tests.test_operations (top-level), astropy.table.tests.test_pickle (top-level), astropy.time.tests.test_corrs (top-level), astropy.uncertainty.tests.test_containers (top-level), astropy.utils.masked.tests.test_containers (top-level), astropy.utils.tests.test_data_info (top-level), astropy.visualization.wcsaxes.tests.test_display_world_coordinates (top-level), astropy.visualization.wcsaxes.tests.test_images (top-level), astropy.visualization.wcsaxes.tests.test_misc (top-level), astropy.visualization.wcsaxes.tests.test_wcsapi (top-level), astropy.wcs.tests.test_utils (top-level), astropy.wcs.tests.test_wcs (top-level), astropy.wcs.wcsapi.conftest (top-level), astropy.wcs.wcsapi.tests.test_fitswcs (top-level), astropy.wcs.wcsapi.tests.test_high_level_api (top-level), astropy.wcs.wcsapi.tests.test_high_level_wcs_wrapper (top-level), astropy.wcs.wcsapi.wrappers.tests.test_sliced_wcs (top-level)
missing module named astropy.modeling.models.Pix2Sky_TAN - imported by astropy.modeling.models (top-level), astropy.modeling.tests.test_quantities_evaluation (top-level), astropy.modeling.tests.test_quantities_model (top-level), astropy.modeling.tests.test_quantities_parameters (top-level)
missing module named astropy.coordinates.angles.angular_separation - imported by astropy.coordinates.angles (delayed), astropy.coordinates.baseframe (delayed)
missing module named astropy.coordinates.angles.offset_by - imported by astropy.coordinates.angles (delayed), astropy.coordinates.sky_coordinate (delayed)
missing module named astropy.coordinates.angles.position_angle - imported by astropy.coordinates.angles (top-level), astropy.coordinates.baseframe (top-level)
missing module named astropy.coordinates.Longitude - imported by astropy.coordinates (conditional), astropy.coordinates.baseframe (conditional), astropy.time.core (delayed), astropy.coordinates.tests.test_angles (top-level), astropy.coordinates.tests.test_api_ape5 (delayed), astropy.coordinates.tests.test_distance (top-level), astropy.coordinates.tests.test_earth (top-level), astropy.coordinates.tests.test_representation (top-level), astropy.coordinates.tests.test_pickle (top-level), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_representation_arithmetic (top-level), astropy.coordinates.tests.test_representation_methods (top-level), astropy.coordinates.tests.test_shape_manipulation (top-level), astropy.coordinates.tests.test_sites (top-level), astropy.coordinates.tests.test_unit_representation (top-level), astropy.io.misc.tests.test_parquet (top-level), astropy.io.misc.tests.test_yaml (top-level), astropy.uncertainty.tests.test_containers (top-level), astropy.utils.masked.tests.test_masked (top-level)
missing module named astropy.coordinates.UnitSphericalRepresentation - imported by astropy.coordinates (top-level), astropy.visualization.wcsaxes.utils (top-level), astropy.visualization.wcsaxes.transforms (top-level), astropy.wcs.utils (delayed), astropy.time.core (delayed), astropy.coordinates.tests.test_intermediate_transformations (top-level), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_representation_arithmetic (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.coordinates.tests.test_sky_coord_velocities (top-level), astropy.io.misc.tests.test_yaml (top-level), astropy.table.tests.test_operations (top-level)
missing module named astropy.coordinates.CartesianRepresentation - imported by astropy.coordinates (top-level), astropy.wcs.utils (top-level), astropy.coordinates.spectral_coordinate (top-level), astropy.time.core (delayed), astropy.coordinates.tests.test_arrays (top-level), astropy.coordinates.tests.test_celestial_transformations (top-level), astropy.coordinates.tests.test_distance (top-level), astropy.coordinates.tests.test_finite_difference_velocities (top-level), astropy.coordinates.tests.test_intermediate_transformations (top-level), astropy.coordinates.tests.test_matching (top-level), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_representation_arithmetic (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.coordinates.tests.test_sky_coord_velocities (top-level), astropy.coordinates.tests.test_spectral_coordinate (top-level), astropy.io.misc.tests.test_parquet (top-level), astropy.io.misc.tests.test_yaml (top-level), astropy.table.tests.test_operations (top-level), astropy.uncertainty.tests.test_containers (top-level)
missing module named astropy.coordinates.ICRS - imported by astropy.coordinates (top-level), astropy.visualization.wcsaxes.wcsapi (top-level), astropy.wcs.wcsapi.fitswcs (top-level), astropy.coordinates.spectral_coordinate (top-level), astropy.wcs.utils (delayed), astropy.time.core (delayed), astropy.coordinates.tests.test_arrays (top-level), astropy.coordinates.tests.test_exceptions (top-level), astropy.coordinates.tests.test_funcs (top-level), astropy.coordinates.tests.test_icrs_observed_transformations (top-level), astropy.coordinates.tests.test_intermediate_transformations (top-level), astropy.coordinates.tests.test_matching (top-level), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_separation (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.coordinates.tests.test_sky_coord_velocities (top-level), astropy.coordinates.tests.test_spectral_coordinate (top-level), astropy.coordinates.tests.test_unit_representation (top-level), astropy.visualization.wcsaxes.tests.test_images (top-level), astropy.wcs.tests.test_utils (delayed), astropy.wcs.wcsapi.tests.test_fitswcs (top-level), astropy.wcs.wcsapi.wrappers.tests.test_sliced_wcs (top-level)
missing module named astropy.coordinates.EarthLocation - imported by astropy.coordinates (top-level), astropy.io.fits.fitstime (top-level), astropy.wcs.wcsapi.fitswcs (delayed), astropy.time.core (delayed, conditional), astropy.coordinates.tests.accuracy.test_altaz_icrs (top-level), astropy.coordinates.tests.test_celestial_transformations (top-level), astropy.coordinates.tests.test_erfa_astrom (top-level), astropy.coordinates.tests.test_frames (top-level), astropy.coordinates.tests.test_iau_fullstack (top-level), astropy.coordinates.tests.test_icrs_observed_transformations (top-level), astropy.coordinates.tests.test_intermediate_transformations (top-level), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_shape_manipulation (top-level), astropy.coordinates.tests.test_sites (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.coordinates.tests.test_skyoffset_transformations (top-level), astropy.coordinates.tests.test_spectral_coordinate (top-level), astropy.coordinates.tests.test_velocity_corrs (top-level), astropy.io.fits.tests.test_fitstime (top-level), astropy.io.misc.tests.test_parquet (top-level), astropy.io.misc.tests.test_yaml (top-level), astropy.table.tests.test_mixin (top-level), astropy.time.tests.test_basic (top-level), astropy.time.tests.test_corrs (top-level), astropy.time.tests.test_mask (top-level), astropy.uncertainty.tests.test_containers (top-level), astropy.wcs.tests.test_utils (top-level), astropy.wcs.wcsapi.tests.test_fitswcs (top-level)
missing module named astropy.utils.masked.get_data_and_mask - imported by astropy.utils.masked (delayed), astropy.utils.masked.function_helpers (delayed), astropy.time.core (top-level)
missing module named astropy.constants.G - imported by astropy.constants (delayed), astropy.units.tests.test_units (delayed)
missing module named astropy.constants.hbar - imported by astropy.constants (top-level), astropy.units.tests.test_physical (top-level)
missing module named astropy.constants.R_sun - imported by astropy.constants (top-level), astropy.coordinates.tests.accuracy.test_ecliptic (top-level)
missing module named astropy.constants.R_earth - imported by astropy.constants (top-level), astropy.coordinates.tests.accuracy.test_ecliptic (top-level)
missing module named astropy.constants.b_wien - imported by astropy.constants (delayed), astropy.constants.tests.test_constant (delayed)
missing module named astropy.constants.g0 - imported by astropy.constants (delayed), astropy.constants.tests.test_constant (delayed)
missing module named astropy.constants.e - imported by astropy.constants (delayed), astropy.constants.tests.test_constant (delayed), astropy.units.tests.test_units (delayed)
missing module named astropy.constants.h - imported by astropy.constants (delayed), astropy.constants.tests.test_constant (delayed), astropy.constants.tests.test_prior_version (delayed)
missing module named ipydatagrid - imported by astropy.table.notebook_backends (delayed), astropy.table.tests.test_jsviewer (delayed)
missing module named bleach - imported by astropy.utils.xml.writer (delayed, conditional), astropy.io.ascii.tests.test_html (delayed)
missing module named astropy.utils.masked.Masked - imported by astropy.utils.masked (delayed), astropy.utils.masked.function_helpers (delayed, conditional), astropy.time (top-level), astropy.time.formats (top-level), astropy.time.core (top-level), astropy.coordinates.representation.base (top-level), astropy.nddata.nddata (top-level), astropy.table.table (top-level), astropy.table.operations (top-level), astropy.nddata.mixins.ndarithmetic (top-level), astropy.coordinates.tests.test_masked (top-level), astropy.io.ascii.tests.test_ecsv (top-level), astropy.io.fits.tests.test_fitstime (top-level), astropy.nddata.tests.test_nddata (top-level), astropy.table.tests.test_init_table (top-level), astropy.table.tests.test_masked (top-level), astropy.table.tests.test_operations (top-level), astropy.time.tests.test_mask (top-level), astropy.units.tests.test_quantity (top-level), astropy.units.tests.test_structured (top-level), astropy.utils.masked.tests.test_containers (top-level), astropy.utils.masked.tests.test_function_helpers (top-level), astropy.utils.masked.tests.test_masked (top-level), astropy.utils.masked.tests.test_table (top-level)
missing module named 'numpy._core.printoptions' - imported by astropy.utils.masked.function_helpers (delayed, conditional)
missing module named 'numpy.lib._function_base_impl' - imported by astropy.utils.masked.function_helpers (conditional), astropy.uncertainty.core (conditional)
missing module named ipykernel - imported by astropy.utils.console (delayed, conditional)
missing module named asdf_astropy - imported by astropy.table (conditional)
missing module named astropy.units.format.Fits - imported by astropy.units.format (delayed), astropy.units.tests.test_format (delayed)
missing module named astropy.units.PrefixUnit - imported by astropy.units (delayed), astropy.units.utils (delayed), astropy.units.tests.test_format (top-level)
missing module named astropy.units.add_enabled_units - imported by astropy.units (top-level), astropy.cosmology.connect (top-level)
missing module named astropy.units.mag - imported by astropy.units (top-level), astropy.modeling.powerlaws (top-level)
missing module named astropy.units.Magnitude - imported by astropy.units (top-level), astropy.modeling.powerlaws (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.UnitTypeError - imported by astropy.units (top-level), astropy.units.function.core (top-level), astropy.units.function.logarithmic (top-level)
missing module named astropy.units.UnrecognizedUnit - imported by astropy.units (top-level), astropy.io.ascii.cds (top-level), astropy.io.fits.tests.test_table (top-level)
missing module named astropy.units.UnitsWarning - imported by astropy.units (top-level), astropy.io.ascii.cds (top-level), astropy.io.fits.tests.test_table (top-level), astropy.table.tests.test_showtable (delayed), astropy.timeseries.io.tests.test_kepler (top-level), astropy.timeseries.tests.test_sampled (top-level), astropy.units.tests.test_format (top-level), astropy.wcs.tests.test_wcsprm (top-level), astropy.wcs.wcsapi.tests.test_fitswcs (top-level)
missing module named astropy.units.IrreducibleUnit - imported by astropy.units (top-level), astropy.coordinates.sky_coordinate_parsers (top-level)
missing module named astropy.units.UnitsError - imported by astropy.units (top-level), astropy.visualization.wcsaxes.formatter_locator (top-level), astropy.units.function.core (top-level), astropy.units.function.logarithmic (top-level), astropy.modeling.core (top-level), astropy.modeling.functional_models (top-level), astropy.modeling.powerlaws (top-level), astropy.nddata.compat (top-level), astropy.modeling.tests.test_quantities_evaluation (top-level), astropy.modeling.tests.test_quantities_fitting (top-level), astropy.modeling.tests.test_quantities_parameters (top-level), astropy.nddata.mixins.tests.test_ndarithmetic (top-level), astropy.timeseries.sampled (top-level), astropy.visualization.wcsaxes.tests.test_formatter_locator (top-level)
missing module named astropy.units.UnitBase - imported by astropy.units (conditional), astropy.units.format.generic (conditional), astropy.units.format.fits (conditional), astropy.units.function.core (top-level), astropy.modeling.bounding_box (conditional), astropy.units.format.base (conditional), astropy.units.format.cds (conditional), astropy.units.format.console (conditional), astropy.units.format.latex (conditional), astropy.units.format.ogip (conditional), astropy.units.format.vounit (conditional), astropy.units.tests.test_format (top-level), astropy.units.tests.test_structured (top-level)
missing module named astropy.units.NamedUnit - imported by astropy.units (conditional), astropy.units.format.generic (conditional), astropy.units.format.base (conditional), astropy.units.format.latex (conditional), astropy.units.format.unicode_format (conditional), astropy.units.format.vounit (conditional)
missing module named astropy.units.CompositeUnit - imported by astropy.units (conditional), astropy.units.format.generic (conditional), astropy.units.function.logarithmic (top-level)
missing module named astropy.units.QuantityInfo - imported by astropy.units (top-level), astropy.table.table (top-level), astropy.io.ascii.tests.test_ecsv (top-level)
missing module named astropy.units.MagUnit - imported by astropy.units (top-level), astropy.modeling.parameters (top-level)
missing module named astropy.units.Equivalency - imported by astropy.units (top-level), astropy.cosmology.units (top-level)
missing module named astropy.units.SpecificTypeQuantity - imported by astropy.units (top-level), astropy.coordinates.angles.core (top-level)
missing module named astropy.units.UnitConversionError - imported by astropy.units (top-level), astropy.time.core (top-level), astropy.units.function.core (top-level), astropy.units.function.logarithmic (top-level), astropy.nddata.nduncertainty (top-level), astropy.nddata.compat (top-level)
missing module named astropy.units.dimensionless_unscaled - imported by astropy.units (delayed, conditional), astropy.units.quantity_helper.function_helpers (delayed, conditional), astropy.modeling.parameters (top-level), astropy.units.equivalencies (top-level), astropy.units.function.core (top-level), astropy.units.function.logarithmic (top-level), astropy.modeling.core (top-level), astropy.modeling.powerlaws (top-level), astropy.nddata.mixins.ndarithmetic (top-level)
missing module named astropy.units.dex - imported by astropy.units (delayed), astropy.units.quantity_helper.function_helpers (delayed), astropy.units.format.cds (delayed), astropy.units.tests.test_format (top-level)
missing module named astropy.units.LogQuantity - imported by astropy.units (delayed), astropy.units.quantity_helper.function_helpers (delayed)
missing module named astropy.units.percent - imported by astropy.units (delayed), astropy.units.quantity_helper.function_helpers (delayed)
missing module named astropy.units.Unit - imported by astropy.units (top-level), astropy.table.column (top-level), astropy.nddata.nddata (top-level), astropy.io.fits.convenience (delayed, conditional), astropy.coordinates.sky_coordinate_parsers (top-level), astropy.io.ascii.cds (top-level), astropy.coordinates.baseframe (conditional), astropy.coordinates.spectral_quantity (top-level), astropy.units.function.core (top-level), astropy.units.function.logarithmic (top-level), astropy.nddata.nduncertainty (top-level), astropy.nddata.compat (top-level), astropy.io.ascii.tests.test_read (top-level), astropy.io.fits.tests.test_table (top-level), astropy.units.tests.test_format (top-level), astropy.units.tests.test_structured (top-level)
missing module named astropy.units.StructuredUnit - imported by astropy.units (top-level), astropy.table.column (top-level), astropy.units.quantity_helper.function_helpers (delayed), astropy.units.tests.test_structured (top-level)
missing module named astropy.units.Quantity - imported by astropy.units (top-level), astropy.table.column (top-level), astropy.units.quantity_helper.function_helpers (delayed, optional), astropy.cosmology._utils (top-level), astropy.cosmology.funcs.optimize (top-level), astropy.modeling.fitting (top-level), astropy.modeling.parameters (top-level), astropy.nddata.nddata (top-level), astropy.table.table (top-level), astropy.table.operations (top-level), astropy.io.fits.convenience (delayed, conditional), astropy.stats.nanfunctions (top-level), astropy.stats.circstats (top-level), astropy.stats.sigma_clipping (top-level), astropy.units.function.core (top-level), astropy.units.typing (top-level), astropy.modeling.core (top-level), astropy.modeling.bounding_box (top-level), astropy.modeling.mappings (top-level), astropy.modeling.functional_models (top-level), astropy.modeling.powerlaws (top-level), astropy.nddata.nduncertainty (top-level), astropy.cosmology.units (conditional), astropy.cosmology.flrw.base (conditional), astropy.io.votable.connect (top-level), astropy.constants.tests.test_constant (top-level), astropy.constants.tests.test_prior_version (top-level), astropy.nddata.mixins.tests.test_ndarithmetic (top-level), astropy.timeseries.sampled (top-level), astropy.timeseries.binned (top-level), astropy.table.tests.test_pickle (top-level), astropy.timeseries.tests.test_sampled (top-level), astropy.units.tests.test_quantity_annotations (top-level), astropy.units.tests.test_structured (top-level), astropy.utils.masked.tests.test_masked (top-level), astropy.utils.masked.tests.test_functions (top-level), astropy.visualization.wcsaxes.tests.test_wcsapi (top-level), astropy.wcs.tests.test_utils (top-level), astropy.wcs.wcsapi.conftest (top-level), astropy.wcs.wcsapi.tests.test_fitswcs (top-level), astropy.wcs.wcsapi.tests.test_high_level_api (top-level), astropy.wcs.wcsapi.wrappers.tests.test_sliced_wcs (top-level)
missing module named astropy._dev - imported by astropy.version (optional)
missing module named compiler - imported by configobj (delayed, conditional), astropy.extern.configobj.configobj (delayed, conditional)
missing module named astropy.utils.ShapedLikeNDArray - imported by astropy.utils (top-level), astropy.table.table (top-level), astropy.coordinates.baseframe (top-level), astropy.coordinates.attributes (top-level), astropy.coordinates.sky_coordinate (top-level)
missing module named astropy.utils.indent - imported by astropy.utils (top-level), astropy.io.fits.hdu.hdulist (top-level)
missing module named astropy.utils.unbroadcast - imported by astropy.utils (top-level), astropy.wcs.utils (top-level), astropy.coordinates.polarization (top-level), astropy.coordinates.tests.test_polarization (top-level), astropy.wcs.tests.test_utils (top-level)
missing module named astropy.utils.isiterable - imported by astropy.utils (delayed), astropy.config.configuration (delayed), astropy.units.quantity_helper.function_helpers (top-level), astropy.coordinates.angles.core (top-level), astropy.modeling.parameters (top-level), astropy.io.fits.column (top-level), astropy.io.fits.header (top-level), astropy.io.fits.hdu.image (top-level), astropy.table.table (top-level), astropy.stats.sigma_clipping (top-level), astropy.wcs.wcsapi.wrappers.sliced_wcs (top-level), astropy.coordinates.funcs (top-level), astropy.modeling.core (top-level), astropy.modeling.bounding_box (top-level), astropy.modeling.spline (top-level), astropy.coordinates.tests.test_representation (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.time.tests.test_basic (top-level), astropy.units.tests.test_quantity (top-level), astropy.visualization.wcsaxes.tests.test_images (top-level)
missing module named astropy.utils.silence - imported by astropy.utils (top-level), astropy.config.configuration (top-level)
missing module named erfa._dev - imported by erfa.version (optional)
missing module named numcodecs - imported by astropy.io.fits.hdu.compressed._codecs (optional)
missing module named astropy.io.fits.Card - imported by astropy.io.fits (top-level), astropy.io.fits.fitstime (top-level)
missing module named astropy.io.fits.append - imported by astropy.io.fits (top-level), astropy.io.fits.connect (top-level)
missing module named astropy.io.fits.TableHDU - imported by astropy.io.fits (top-level), astropy.io.fits.connect (top-level)
missing module named astropy.io.fits.HDUList - imported by astropy.io.fits (top-level), astropy.io.fits.connect (top-level), astropy.io.fits.tests.test_connect (top-level), astropy.io.fits.tests.test_fitsdiff (top-level), astropy.timeseries.io.tests.test_kepler (top-level)
missing module named astropy.io.fits.GroupsHDU - imported by astropy.io.fits (top-level), astropy.io.fits.connect (top-level)
missing module named astropy.io.fits.BinTableHDU - imported by astropy.io.fits (top-level), astropy.io.fits.connect (top-level), astropy.io.fits.tests.test_connect (top-level), astropy.timeseries.io.tests.test_kepler (top-level)
missing module named extension_helpers - imported by astropy.utils.xml.setup_package (top-level), astropy.wcs.setup_package (top-level)
missing module named 'hypothesis.extra' - imported by thinc.tests.strategies (top-level), torch.testing._internal.hypothesis_utils (top-level), astropy.io.fits.hdu.compressed.tests.test_compressed (top-level), astropy.table.tests.test_groups (top-level), astropy.time.tests.test_precision (top-level), astropy.utils.tests.test_shapes (top-level)
missing module named test_package - imported by astropy.utils.tests.test_data (delayed, optional)
missing module named array_api_strict - imported by astropy.units.tests.test_quantity_array_methods (delayed), astropy.units.tests.test_units (delayed)
missing module named astropy.uncertainty.Distribution - imported by astropy.uncertainty (delayed), astropy.uncertainty.function_helpers (delayed), astropy.uncertainty.tests.test_containers (top-level), astropy.uncertainty.tests.test_functions (top-level)
missing module named 'numpy.lib._stride_tricks_impl' - imported by astropy.uncertainty.core (conditional)
missing module named 'hypothesis.strategies' - imported by spacy.tests.parser.test_nn_beam (top-level), thinc.tests.strategies (top-level), spacy.tests.pipeline.test_edit_tree_lemmatizer (top-level), spacy.tests.tokenizer.test_explain (top-level), thinc.tests.backends.test_ops (top-level), thinc.tests.layers.test_uniqued (top-level), thinc.tests.shims.test_pytorch_grad_scaler (top-level), torch.testing._internal.hypothesis_utils (top-level), astropy.table.tests.test_groups (top-level), astropy.time.tests.test_precision (top-level)
missing module named coverage - imported by astropy.tests.command (delayed, optional)
missing module named fitsio - imported by astropy.io.fits.hdu.compressed.tests.test_fitsio (conditional)
missing module named ply - imported by astropy.extern.ply.cpp (conditional)
missing module named skyfield - imported by astropy.coordinates.tests.test_solar_system (conditional)
missing module named pytest_remotedata - imported by astropy.coordinates.tests.test_name_resolve (top-level)
missing module named ephem - imported by astropy.coordinates.tests.accuracy.test_altaz_icrs (delayed)
missing module named 'starlink.Ast' - imported by astropy.coordinates.tests.accuracy.generate_ref_ast (delayed)
missing module named starlink - imported by astropy.coordinates.tests.accuracy.generate_ref_ast (delayed)
missing module named pytest_astropy_header - imported by astropy.conftest (optional)
missing module named xdist - imported by astropy.tests.runner (delayed, conditional, optional)
missing module named tkFileDialog - imported by imageio.plugins._tifffile (delayed, optional)
missing module named Tkinter - imported by imageio.plugins._tifffile (delayed, optional)
missing module named tifffile_geodb - imported by imageio.plugins._tifffile (delayed, optional)
missing module named imageio.plugins.tifffile_geodb - imported by imageio.plugins._tifffile (delayed, optional)
missing module named imagecodecs - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named zstd - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named 'backports.lzma' - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named bsdf_cli - imported by imageio.plugins._bsdf (conditional)
missing module named tabulate - imported by torch.ao.ns.fx.n_shadows_utils (delayed, optional), torch._inductor.wrapper_benchmark (delayed), torch.utils.flop_counter (delayed), torch._dynamo.utils (delayed, optional), torch._dynamo.backends.distributed (delayed, conditional, optional), torch.fx.graph (delayed, optional), torch.ao.quantization.fx._model_report.model_report_visualizer (optional), torch.distributed._tools.mem_tracker (delayed, optional), torch.distributed._tools.sac_estimator (delayed, optional), torch.distributed.tensor.debug._visualize_sharding (delayed, optional), torch.distributed.tensor.debug._op_coverage (delayed), torch.utils.benchmark.utils.compile (optional)
missing module named torchaudio - imported by transformers.models.audio_spectrogram_transformer.feature_extraction_audio_spectrogram_transformer (conditional), transformers.models.musicgen_melody.feature_extraction_musicgen_melody (conditional), transformers.pipelines.audio_classification (delayed, conditional), transformers.pipelines.automatic_speech_recognition (delayed, conditional), torch.utils.data.datapipes.utils.decoder (delayed, optional)
missing module named 'torchvision.io' - imported by transformers.models.deformable_detr.image_processing_deformable_detr_fast (conditional), transformers.models.detr.image_processing_detr_fast (conditional), torch.utils.data.datapipes.utils.decoder (delayed, optional)
missing module named torcharrow - imported by torch.utils.data.datapipes.iter.callable (delayed, conditional, optional)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named Cython - imported by setuptools.command.build_ext (optional)
missing module named 'conda.cli' - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named conda - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named triton - imported by torch._utils_internal (delayed, conditional), torch._inductor.runtime.hints (delayed, optional), torch._dynamo.logging (conditional, optional), torch.utils._triton (delayed), torch._inductor.runtime.autotune_cache (conditional), torch._inductor.codegen.wrapper (delayed, conditional), torch._higher_order_ops.triton_kernel_wrap (delayed), torch._inductor.kernel.mm_common (delayed), torch._inductor.kernel.mm_plus_mm (delayed), transformers.integrations.finegrained_fp8 (conditional), torch._inductor.runtime.coordinate_descent_tuner (optional), torch._inductor.runtime.triton_heuristics (conditional, optional), torch._inductor.runtime.triton_helpers (top-level), torch.sparse._triton_ops_meta (delayed, conditional), torch.sparse._triton_ops (conditional), torch._dynamo.utils (conditional), torch._inductor.compile_worker.__main__ (optional), torch.testing._internal.inductor_utils (conditional), torch.testing._internal.triton_utils (conditional)
missing module named 'triton.runtime' - imported by torch._inductor.runtime.runtime_utils (delayed, optional), torch._library.triton (delayed), torch.utils._triton (delayed), torch._inductor.select_algorithm (delayed, optional), torch._higher_order_ops.triton_kernel_wrap (delayed, conditional), torch._inductor.fx_passes.reinplace (delayed, conditional), torch._inductor.runtime.triton_heuristics (conditional, optional), torch._inductor.codecache (delayed, conditional), torch._dynamo.variables.builder (delayed, conditional), torch._inductor.ir (delayed), torch._inductor.utils (delayed)
missing module named 'triton.backends' - imported by torch._inductor.runtime.hints (conditional, optional), torch.utils._triton (delayed), torch._higher_order_ops.triton_kernel_wrap (delayed, optional), torch._inductor.runtime.triton_heuristics (conditional, optional)
missing module named 'triton.language' - imported by torch.utils._triton (delayed, conditional, optional), torch._inductor.codegen.wrapper (delayed), torch._inductor.codegen.triton_split_scan (delayed), transformers.integrations.finegrained_fp8 (conditional), torch._inductor.runtime.triton_helpers (top-level), torch.sparse._triton_ops (conditional), torch.testing._internal.triton_utils (conditional)
missing module named 'triton.tools' - imported by torch.utils._triton (delayed, conditional, optional), torch._higher_order_ops.triton_kernel_wrap (delayed, conditional), torch._dynamo.variables.builder (delayed, conditional)
missing module named 'triton.compiler' - imported by torch._inductor.runtime.hints (conditional, optional), torch.utils._triton (delayed, optional), torch._inductor.async_compile (delayed, optional), torch._inductor.codegen.triton (delayed), torch._higher_order_ops.triton_kernel_wrap (delayed), torch._inductor.runtime.triton_heuristics (conditional, optional), torch._inductor.scheduler (delayed), torch._inductor.codecache (delayed, optional)
missing module named 'torch._C._profiler' - imported by torch.utils._traceback (delayed), torch.testing._internal.logging_tensor (top-level), torch.profiler (top-level), torch.autograd.profiler (top-level), torch.profiler.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.cuda._memory_viz (delayed), torch.autograd (top-level), torch.profiler._pattern_matcher (top-level)
missing module named flint - imported by sympy.external.gmpy (delayed, optional), sympy.polys.polyutils (conditional), sympy.polys.factortools (conditional), sympy.polys.polyclasses (conditional), sympy.polys.domains.groundtypes (conditional), sympy.polys.domains.finitefield (conditional)
missing module named pyogg - imported by pyglet.media.codecs.pyogg (top-level)
missing module named 'gi.repository' - imported by pyglet.media.codecs.gstreamer (optional)
missing module named all - imported by sympy.testing.runtests (delayed, optional)
missing module named 'IPython.Shell' - imported by sympy.interactive.session (delayed, conditional)
missing module named 'IPython.frontend' - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional)
missing module named 'IPython.terminal' - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional)
missing module named 'IPython.iplib' - imported by sympy.interactive.printing (delayed, optional)
missing module named pysat - imported by sympy.logic.algorithms.minisat22_wrapper (delayed)
missing module named pycosat - imported by sympy.logic.algorithms.pycosat_wrapper (delayed)
missing module named 'sage.all' - imported by sympy.core.function (delayed)
missing module named 'sage.interfaces' - imported by sympy.core.basic (delayed)
missing module named pytest_subtests - imported by torch.testing._internal.opinfo.core (delayed, conditional, optional)
missing module named 'torch._C._distributed_c10d' - imported by torch.distributed (conditional), torch.distributed.device_mesh (conditional), torch.distributed.distributed_c10d (top-level), torch.distributed.tensor._collective_utils (top-level), torch.distributed.rpc (conditional), torch.distributed._shard.sharded_tensor.reshard (top-level), torch.distributed._shard.sharding_spec.chunk_sharding_spec_ops.embedding_bag (top-level), torch.testing._internal.distributed.fake_pg (top-level), torch._inductor.codegen.wrapper (delayed, optional), torch.distributed._symmetric_memory (top-level), torch._dynamo.variables.distributed (delayed), torch.distributed.constants (top-level), torch.distributed.elastic.control_plane (delayed), torch.testing._internal.common_distributed (top-level), torch.testing._internal.distributed.multi_threaded_pg (top-level)
missing module named torchvision - imported by torch._inductor.utils (delayed, optional), transformers.image_utils (conditional), torch.testing._internal.common_quantization (optional), torch.testing._internal.distributed.distributed_test (optional)
missing module named 'xmlrunner.result' - imported by torch.testing._internal.common_utils (delayed, conditional)
missing module named xmlrunner - imported by torch.testing._internal.common_utils (delayed, conditional)
missing module named torch.nn.Sequential - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ModuleList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ModuleDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named expecttest - imported by torch.testing._internal.common_utils (top-level)
missing module named torch.ao.quantization.QConfigMapping - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.custom_config (top-level), torch.ao.ns.fx.n_shadows_utils (top-level), torch.ao.ns.fx.qconfig_multi_mapping (top-level), torch.ao.ns._numeric_suite_fx (top-level), torch.ao.quantization.fx.lstm_utils (top-level), torch.ao.quantization.pt2e.prepare (top-level), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.float_qparams_weight_only_qconfig - imported by torch.ao.quantization (delayed, conditional), torch.ao.nn.quantized.modules.embedding_ops (delayed, conditional), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.QConfig - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.qconfig_mapping_utils (top-level), torch.ao.quantization.fx.lstm_utils (top-level), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.QuantType - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.utils (top-level), torch.testing._internal.common_quantization (top-level)
missing module named bitsandbytes - imported by transformers.utils.import_utils (delayed), transformers.models.rwkv.modeling_rwkv (delayed), transformers.quantizers.quantizer_bnb_4bit (delayed), transformers.quantizers.quantizer_bnb_8bit (delayed), transformers.modeling_utils (delayed, conditional), transformers.integrations.bitsandbytes (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named intel_extension_for_pytorch - imported by transformers.utils.import_utils (delayed, conditional), transformers.trainer (delayed)
missing module named torch_musa - imported by transformers.utils.import_utils (delayed)
missing module named torch_mlu - imported by transformers.utils.import_utils (delayed)
missing module named torch_npu - imported by transformers.utils.import_utils (delayed)
missing module named mamba_ssm - imported by transformers.utils.import_utils (delayed, conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level)
missing module named 'mlx.core' - imported by transformers.utils.generic (delayed)
missing module named 'jax.core' - imported by transformers.utils.generic (delayed, conditional)
missing module named 'jax.random' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'flax.traverse_util' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'flax.serialization' - imported by transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level)
missing module named msgpack - imported by transformers.modeling_flax_utils (top-level)
missing module named 'flax.linen' - imported by transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named transformers.models.pop2piano.Pop2PianoTokenizer - imported by transformers.models.pop2piano (conditional, optional), transformers (conditional, optional)
missing module named transformers.models.pop2piano.Pop2PianoProcessor - imported by transformers.models.pop2piano (conditional, optional), transformers (conditional, optional)
missing module named transformers.models.pop2piano.Pop2PianoFeatureExtractor - imported by transformers.models.pop2piano (conditional, optional), transformers (conditional, optional)
missing module named datasets - imported by transformers.modeling_tf_utils (delayed), transformers.modelcard (delayed, conditional), transformers.models.rag.retrieval_rag (conditional), transformers.trainer (conditional), transformers.agents.text_to_speech (conditional), transformers.trainer_seq2seq (conditional)
missing module named deepspeed - imported by transformers.integrations.deepspeed (delayed), transformers.models.distilbert.modeling_distilbert (delayed, conditional), transformers.models.esm.modeling_esmfold (delayed, conditional, optional), transformers.models.fsmt.modeling_fsmt (delayed, conditional), transformers.models.hubert.modeling_hubert (delayed, conditional), transformers.models.seamless_m4t.modeling_seamless_m4t (delayed, conditional), transformers.models.sew.modeling_sew (delayed, conditional), transformers.models.sew_d.modeling_sew_d (delayed, conditional), transformers.models.speecht5.modeling_speecht5 (delayed, conditional), transformers.models.unispeech.modeling_unispeech (delayed, conditional), transformers.models.unispeech_sat.modeling_unispeech_sat (delayed, conditional), transformers.models.wav2vec2.modeling_wav2vec2 (delayed, conditional), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (delayed, conditional), transformers.models.wavlm.modeling_wavlm (delayed, conditional), transformers.modeling_utils (delayed, conditional)
missing module named 'accelerate.utils' - imported by transformers.trainer_utils (delayed, conditional), transformers.training_args (delayed, conditional), transformers.integrations.deepspeed (delayed, conditional), transformers.models.mask2former.modeling_mask2former (conditional), transformers.models.maskformer.modeling_maskformer (conditional), transformers.models.oneformer.modeling_oneformer (conditional), transformers.loss.loss_for_object_detection (conditional), transformers.quantizers.quantizer_bnb_4bit (delayed, conditional), transformers.quantizers.quantizer_finegrained_fp8 (delayed), transformers.quantizers.quantizer_quanto (delayed, conditional), transformers.quantizers.quantizer_torchao (delayed, conditional), transformers.modeling_utils (conditional), transformers.integrations.bitsandbytes (conditional), transformers.integrations.integration_utils (delayed), transformers.trainer (delayed, conditional), transformers.integrations.peft (conditional), transformers.agents.tools (conditional), transformers.models.deprecated.deta.modeling_deta (conditional)
missing module named smdistributed - imported by transformers.trainer_pt_utils (conditional)
missing module named 'peft.utils' - imported by transformers.trainer (delayed, conditional), transformers.integrations.peft (delayed)
missing module named wandb - imported by transformers.integrations.integration_utils (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named 'torch_xla.experimental' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'ray.train' - imported by transformers.integrations.integration_utils (delayed), transformers.trainer (delayed, conditional)
missing module named schedulefree - imported by transformers.trainer (delayed, conditional)
missing module named 'torchao.prototype' - imported by transformers.trainer (delayed, conditional)
missing module named grokadamw - imported by transformers.trainer (delayed, conditional)
missing module named lomo_optim - imported by transformers.trainer (delayed, conditional)
missing module named apollo_torch - imported by transformers.trainer (delayed, conditional)
missing module named galore_torch - imported by transformers.trainer (delayed, conditional)
missing module named 'torchdistx.optimizers' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'bitsandbytes.optim' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'apex.optimizers' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'torch_npu.optim' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'torch_xla.amp' - imported by transformers.trainer (delayed, conditional, optional)
missing module named liger_kernel - imported by transformers.trainer (delayed, conditional)
missing module named optuna - imported by transformers.integrations.integration_utils (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named 'accelerate.data_loader' - imported by transformers.trainer (conditional)
missing module named 'accelerate.state' - imported by transformers.training_args (conditional), transformers.trainer (conditional)
missing module named accelerate - imported by transformers.integrations.aqlm (delayed), transformers.models.bark.modeling_bark (delayed, conditional), transformers.models.mask2former.modeling_mask2former (conditional), transformers.models.maskformer.modeling_maskformer (conditional), transformers.models.oneformer.modeling_oneformer (conditional), transformers.loss.loss_for_object_detection (conditional), transformers.integrations.finegrained_fp8 (conditional), transformers.modeling_utils (conditional), transformers.integrations.bitnet (conditional), transformers.integrations.bitsandbytes (conditional), transformers.integrations.eetq (conditional), transformers.integrations.fbgemm_fp8 (conditional), transformers.integrations.higgs (delayed), transformers.trainer (conditional), transformers.integrations.peft (conditional), transformers.integrations.quanto (delayed), transformers.integrations.spqr (delayed, conditional), transformers.integrations.vptq (top-level), transformers.agents.tools (conditional), transformers.models.deprecated.deta.modeling_deta (conditional)
missing module named peft - imported by transformers.models.data2vec.modeling_data2vec_audio (delayed, conditional), transformers.trainer (delayed, conditional), transformers.integrations.peft (delayed)
missing module named 'smdistributed.modelparallel' - imported by transformers.training_args (conditional), transformers.modeling_utils (conditional), transformers.trainer (conditional)
missing module named 'torch_xla.runtime' - imported by transformers.trainer (conditional)
missing module named 'torch_xla.distributed' - imported by torch.distributed.tensor._api (delayed, conditional, optional), transformers.training_args (conditional), transformers.integrations.tpu (delayed, conditional), transformers.trainer (delayed, conditional, optional)
missing module named 'torch_xla.debug' - imported by transformers.trainer (conditional)
missing module named apex - imported by transformers.models.longt5.modeling_longt5 (optional), transformers.trainer (conditional)
missing module named 'optimum.bettertransformer' - imported by transformers.modeling_utils (delayed)
missing module named 'optimum.version' - imported by transformers.modeling_utils (delayed)
missing module named gguf - imported by transformers.modeling_gguf_pytorch_utils (delayed, conditional)
missing module named 'accelerate.hooks' - imported by transformers.generation.utils (conditional), transformers.quantizers.quantizer_hqq (conditional), transformers.modeling_utils (conditional), transformers.integrations.bitsandbytes (conditional)
missing module named vptq - imported by transformers.quantizers.quantizer_vptq (delayed, conditional), transformers.integrations.vptq (top-level)
missing module named 'torchao.quantization' - imported by transformers.quantizers.quantizer_torchao (delayed)
missing module named 'torchao.dtypes' - imported by transformers.quantizers.quantizer_torchao (delayed)
missing module named 'optimum.quanto' - imported by transformers.cache_utils (delayed, conditional), transformers.quantizers.quantizer_quanto (delayed, conditional), transformers.integrations.quanto (delayed, conditional)
missing module named 'hqq.core' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_hqq (delayed, conditional), transformers.integrations.hqq (delayed, conditional)
missing module named 'flute.utils' - imported by transformers.quantizers.quantizer_higgs (delayed)
missing module named flute - imported by transformers.quantizers.quantizer_higgs (delayed)
missing module named 'optimum.gptq' - imported by transformers.quantizers.quantizer_gptq (delayed)
missing module named eetq - imported by transformers.quantizers.quantizer_eetq (delayed, optional), transformers.integrations.eetq (conditional)
missing module named 'compressed_tensors.config' - imported by transformers.quantizers.quantizer_compressed_tensors (delayed)
missing module named 'compressed_tensors.quantization' - imported by transformers.utils.quantization_config (delayed), transformers.quantizers.quantizer_compressed_tensors (delayed, conditional)
missing module named 'compressed_tensors.compressors' - imported by transformers.quantizers.quantizer_compressed_tensors (delayed)
missing module named torch.nn.MSELoss - imported by torch.nn (top-level), transformers.models.roformer.modeling_roformer (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.swin.modeling_swin (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.swinv2.modeling_swinv2 (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.vivit.modeling_vivit (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zamba2.modeling_zamba2 (top-level), transformers.loss.loss_utils (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.mmbt.modeling_mmbt (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level)
missing module named torch.nn.BCEWithLogitsLoss - imported by torch.nn (top-level), transformers.models.roformer.modeling_roformer (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.swin.modeling_swin (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.speecht5.modeling_speecht5 (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.swinv2.modeling_swinv2 (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zamba2.modeling_zamba2 (top-level), transformers.loss.loss_utils (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level)
missing module named flash_attn - imported by transformers.modeling_flash_attention_utils (conditional), transformers.models.qwen2_5_vl.modeling_qwen2_5_vl (conditional), transformers.models.qwen2_vl.modeling_qwen2_vl (conditional)
missing module named torch.nn.Identity - imported by torch.nn (top-level), transformers.modeling_utils (top-level)
missing module named torch.nn.CrossEntropyLoss - imported by torch.nn (top-level), transformers.modeling_utils (top-level), transformers.models.encoder_decoder.modeling_encoder_decoder (top-level), transformers.models.roformer.modeling_roformer (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.blenderbot.modeling_blenderbot (top-level), transformers.models.blenderbot_small.modeling_blenderbot_small (top-level), transformers.models.blip.modeling_blip_text (top-level), transformers.models.blip_2.modeling_blip_2 (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.bridgetower.modeling_bridgetower (top-level), transformers.models.bros.modeling_bros (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.chameleon.modeling_chameleon (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.clvp.modeling_clvp (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.cpmant.modeling_cpmant (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_audio (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.dpt.modeling_dpt (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.falcon_mamba.modeling_falcon_mamba (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.fsmt.modeling_fsmt (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.hubert.modeling_hubert (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.idefics.modeling_idefics (top-level), transformers.models.idefics2.modeling_idefics2 (top-level), transformers.models.idefics3.modeling_idefics3 (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.instructblip.modeling_instructblip (top-level), transformers.models.instructblipvideo.modeling_instructblipvideo (top-level), transformers.models.kosmos2.modeling_kosmos2 (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.longt5.modeling_longt5 (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.lxmert.modeling_lxmert (top-level), transformers.models.m2m_100.modeling_m2m_100 (top-level), transformers.models.mamba.modeling_mamba (top-level), transformers.models.mamba2.modeling_mamba2 (top-level), transformers.models.marian.modeling_marian (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.swin.modeling_swin (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.moshi.modeling_moshi (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.musicgen.modeling_musicgen (top-level), transformers.models.musicgen_melody.modeling_musicgen_melody (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nllb_moe.modeling_nllb_moe (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.pegasus.modeling_pegasus (top-level), transformers.models.pegasus_x.modeling_pegasus_x (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pop2piano.modeling_pop2piano (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.qwen2_5_vl.modeling_qwen2_5_vl (top-level), transformers.models.qwen2_vl.modeling_qwen2_vl (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.seamless_m4t.modeling_seamless_m4t (top-level), transformers.models.seamless_m4t_v2.modeling_seamless_m4t_v2 (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.sew.modeling_sew (top-level), transformers.models.sew_d.modeling_sew_d (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.speech_encoder_decoder.modeling_speech_encoder_decoder (top-level), transformers.models.speech_to_text.modeling_speech_to_text (top-level), transformers.models.speecht5.modeling_speecht5 (top-level), transformers.models.splinter.modeling_splinter (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.swinv2.modeling_swinv2 (top-level), transformers.models.switch_transformers.modeling_switch_transformers (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.trocr.modeling_trocr (top-level), transformers.models.udop.modeling_udop (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.unispeech.modeling_unispeech (top-level), transformers.models.unispeech_sat.modeling_unispeech_sat (top-level), transformers.models.upernet.modeling_upernet (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vilt.modeling_vilt (top-level), transformers.models.vision_encoder_decoder.modeling_vision_encoder_decoder (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.vivit.modeling_vivit (top-level), transformers.models.wav2vec2.modeling_wav2vec2 (top-level), transformers.models.wav2vec2_bert.modeling_wav2vec2_bert (top-level), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (top-level), transformers.models.wavlm.modeling_wavlm (top-level), transformers.models.whisper.modeling_whisper (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zamba2.modeling_zamba2 (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.mmbt.modeling_mmbt (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.realm.modeling_realm (top-level), transformers.models.deprecated.speech_to_text_2.modeling_speech_to_text_2 (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level)
missing module named optimum - imported by transformers.cache_utils (delayed, conditional)
missing module named hqq - imported by transformers.cache_utils (conditional)
missing module named transformers.models.timm_wrapper.TimmWrapperImageProcessor - imported by transformers.models.timm_wrapper (conditional, optional), transformers (conditional, optional)
missing module named 'torchvision.transforms' - imported by transformers.image_utils (conditional), transformers.image_processing_utils_fast (conditional), transformers.models.convnext.image_processing_convnext_fast (conditional), transformers.models.deformable_detr.image_processing_deformable_detr_fast (conditional), transformers.models.depth_pro.image_processing_depth_pro_fast (conditional), transformers.models.detr.image_processing_detr_fast (conditional), transformers.models.llava.image_processing_llava_fast (conditional), transformers.models.llava_next.image_processing_llava_next_fast (conditional), transformers.models.llava_onevision.image_processing_llava_onevision_fast (conditional), transformers.models.pixtral.image_processing_pixtral_fast (conditional), transformers.models.qwen2_vl.image_processing_qwen2_vl_fast (conditional), transformers.models.rt_detr.image_processing_rt_detr_fast (conditional)
missing module named transformers.models.llava.LlavaImageProcessor - imported by transformers.models.llava (conditional, optional), transformers (conditional, optional)
missing module named yt_dlp - imported by transformers.image_utils (conditional)
missing module named decord - imported by transformers.image_utils (conditional)
missing module named tiktoken - imported by transformers.convert_slow_tokenizer (delayed, optional)
missing module named rjieba - imported by transformers.models.roformer.tokenization_roformer (delayed, optional), transformers.models.roformer.tokenization_utils (delayed, optional)
missing module named sentencepiece - imported by transformers.convert_slow_tokenizer (delayed, conditional), transformers.models.albert.tokenization_albert (top-level), transformers.models.barthez.tokenization_barthez (top-level), transformers.models.bartpho.tokenization_bartpho (top-level), transformers.models.bert_generation.tokenization_bert_generation (top-level), transformers.models.bert_japanese.tokenization_bert_japanese (conditional), transformers.models.big_bird.tokenization_big_bird (top-level), transformers.models.camembert.tokenization_camembert (top-level), transformers.models.code_llama.tokenization_code_llama (top-level), transformers.models.cpm.tokenization_cpm (top-level), transformers.models.deberta_v2.tokenization_deberta_v2 (top-level), transformers.models.fnet.tokenization_fnet (top-level), transformers.models.gemma.tokenization_gemma (top-level), transformers.models.gpt_sw3.tokenization_gpt_sw3 (top-level), transformers.models.layoutxlm.tokenization_layoutxlm (top-level), transformers.models.xlm_roberta.tokenization_xlm_roberta (top-level), transformers.models.llama.tokenization_llama (top-level), transformers.models.m2m_100.tokenization_m2m_100 (top-level), transformers.models.marian.tokenization_marian (top-level), transformers.models.mbart.tokenization_mbart (top-level), transformers.models.mbart50.tokenization_mbart50 (top-level), transformers.models.mluke.tokenization_mluke (top-level), transformers.models.t5.tokenization_t5 (top-level), transformers.models.nllb.tokenization_nllb (top-level), transformers.models.pegasus.tokenization_pegasus (top-level), transformers.models.plbart.tokenization_plbart (top-level), transformers.models.reformer.tokenization_reformer (top-level), transformers.models.rembert.tokenization_rembert (top-level), transformers.models.seamless_m4t.tokenization_seamless_m4t (top-level), transformers.models.siglip.tokenization_siglip (top-level), transformers.models.speech_to_text.tokenization_speech_to_text (top-level), transformers.models.speecht5.tokenization_speecht5 (top-level), transformers.models.udop.tokenization_udop (top-level), transformers.models.xglm.tokenization_xglm (top-level), transformers.models.xlnet.tokenization_xlnet (top-level), transformers.models.deprecated.ernie_m.tokenization_ernie_m (top-level), transformers.models.deprecated.xlm_prophetnet.tokenization_xlm_prophetnet (delayed, optional)
missing module named sentencepiece_model_pb2 - imported by tokenizers.implementations.sentencepiece_unigram (delayed, optional)
missing module named 'transformers.utils.dummies_sentencepiece_and_tokenizers_objects' - imported by transformers (conditional, optional)
missing module named transformers.models.mt5.MT5TokenizerFast - imported by transformers.models.mt5 (conditional, optional), transformers (conditional, optional)
missing module named torchao - imported by transformers.utils.quantization_config (delayed, conditional)
missing module named compressed_tensors - imported by transformers.utils.quantization_config (delayed)
missing module named ray - imported by transformers.trainer_utils (delayed), transformers.integrations.integration_utils (delayed)
missing module named mlx - imported by transformers.tokenization_utils_base (delayed, conditional)
missing module named pyctcdecode - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed, conditional), transformers.pipelines.automatic_speech_recognition (conditional), transformers.pipelines (delayed, conditional, optional)
missing module named kenlm - imported by transformers.pipelines (delayed, conditional, optional)
missing module named torch.nn.L1Loss - imported by torch.nn (top-level), transformers.models.speecht5.modeling_speecht5 (top-level)
missing module named causal_conv1d - imported by transformers.models.jamba.modeling_jamba (conditional), transformers.models.bamba.modeling_bamba (conditional), transformers.models.falcon_mamba.modeling_falcon_mamba (conditional), transformers.models.mamba.modeling_mamba (conditional), transformers.models.mamba2.modeling_mamba2 (conditional), transformers.models.zamba.modeling_zamba (conditional), transformers.models.zamba2.modeling_zamba2 (conditional)
missing module named 'mamba_ssm.ops' - imported by transformers.models.jamba.modeling_jamba (conditional), transformers.models.bamba.modeling_bamba (conditional), transformers.models.falcon_mamba.modeling_falcon_mamba (conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (conditional), transformers.models.mamba.modeling_mamba (conditional), transformers.models.mamba2.modeling_mamba2 (conditional), transformers.models.zamba.modeling_zamba (conditional), transformers.models.zamba2.modeling_zamba2 (conditional)
missing module named fast_lsh_cumulation - imported by transformers.models.yoso.modeling_yoso (delayed)
missing module named pycocotools - imported by transformers.models.conditional_detr.image_processing_conditional_detr (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr_fast (delayed, optional), transformers.models.detr.image_processing_detr (delayed, optional), transformers.models.detr.image_processing_detr_fast (delayed, optional), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, optional), transformers.models.yolos.image_processing_yolos (delayed, optional), transformers.models.deprecated.deta.image_processing_deta (delayed, optional)
missing module named tf2onnx - imported by transformers.onnx.convert (delayed)
missing module named onnxruntime - imported by torch.onnx._internal.exporter._onnx_program (delayed, conditional), transformers.onnx.convert (delayed, optional), torch.onnx._internal.onnxruntime (delayed, conditional), torch.onnx._internal._exporter_legacy (conditional), torch.onnx.verification (delayed, optional)
missing module named flax - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_outputs (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.generation.flax_utils (top-level)
missing module named pythainlp - imported by transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named Mykytea - imported by transformers.models.flaubert.tokenization_flaubert (delayed, conditional, optional), transformers.models.herbert.tokenization_herbert (delayed, conditional, optional), transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named sacremoses - imported by transformers.models.biogpt.tokenization_biogpt (delayed, optional), transformers.models.flaubert.tokenization_flaubert (delayed, optional), transformers.models.fsmt.tokenization_fsmt (delayed, optional), transformers.models.herbert.tokenization_herbert (delayed, optional), transformers.models.marian.tokenization_marian (delayed, optional), transformers.models.xlm.tokenization_xlm (delayed, optional), transformers.models.deprecated.transfo_xl.tokenization_transfo_xl (conditional)
missing module named 'jax.lax' - imported by transformers.generation.flax_logits_process (top-level)
missing module named 'peft.tuners' - imported by transformers.models.unispeech_sat.modeling_unispeech_sat (delayed, conditional), transformers.models.wav2vec2.modeling_wav2vec2 (delayed, conditional), transformers.models.wav2vec2_bert.modeling_wav2vec2_bert (delayed, conditional), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (delayed, conditional), transformers.models.wavlm.modeling_wavlm (delayed, conditional), transformers.integrations.peft (delayed)
missing module named 'pyctcdecode.constants' - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed)
missing module named 'pyctcdecode.alphabet' - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed)
missing module named 'phonemizer.separator' - imported by transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (delayed)
missing module named 'phonemizer.backend' - imported by transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (delayed)
missing module named uroman - imported by transformers.models.vits.tokenization_vits (conditional)
missing module named phonemizer - imported by transformers.models.vits.tokenization_vits (conditional)
missing module named torch.nn.LogSoftmax - imported by torch.nn (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level)
missing module named torch.nn.KLDivLoss - imported by torch.nn (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level)
missing module named transformers.models.timm_wrapper.processing_timm_wrapper - imported by transformers.models.timm_wrapper (conditional)
missing module named timm - imported by transformers.models.timm_backbone.modeling_timm_backbone (conditional), transformers.models.conditional_detr.modeling_conditional_detr (conditional), transformers.models.deformable_detr.modeling_deformable_detr (conditional), transformers.models.detr.modeling_detr (conditional), transformers.models.grounding_dino.modeling_grounding_dino (conditional), transformers.models.table_transformer.modeling_table_transformer (conditional), transformers.models.timm_wrapper.modeling_timm_wrapper (conditional)
missing module named 'timm.data' - imported by transformers.models.timm_wrapper.configuration_timm_wrapper (conditional)
missing module named tensorflow_probability - imported by transformers.models.groupvit.modeling_tf_groupvit (conditional, optional), transformers.models.tapas.modeling_tf_tapas (conditional, optional)
missing module named 'apex.normalization' - imported by transformers.models.t5.modeling_t5 (optional), transformers.models.pix2struct.modeling_pix2struct (optional), transformers.models.pop2piano.modeling_pop2piano (optional)
missing module named 'torchaudio.compliance' - imported by transformers.models.speech_to_text.feature_extraction_speech_to_text (conditional)
missing module named torch.nn.LayerNorm - imported by torch.nn (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.esm.modeling_esmfold (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.fsmt.modeling_fsmt (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.prophetnet.modeling_prophetnet (top-level), transformers.models.qwen2_vl.modeling_qwen2_vl (top-level), transformers.models.sew_d.modeling_sew_d (top-level), transformers.models.deprecated.jukebox.modeling_jukebox (top-level), transformers.models.deprecated.xlm_prophetnet.modeling_xlm_prophetnet (top-level)
missing module named 'torchvision.ops' - imported by transformers.models.omdet_turbo.processing_omdet_turbo (conditional), transformers.models.sam.image_processing_sam (conditional), transformers.models.deprecated.deta.image_processing_deta (conditional), transformers.models.deprecated.deta.modeling_deta (conditional)
missing module named faiss - imported by transformers.models.rag.retrieval_rag (conditional)
missing module named 'flash_attn.layers' - imported by transformers.models.modernbert.modeling_modernbert (conditional), transformers.models.qwen2_5_vl.modeling_qwen2_5_vl (conditional)
missing module named 'flash_attn.bert_padding' - imported by transformers.models.chameleon.modeling_chameleon (conditional), transformers.models.paligemma.modeling_paligemma (conditional)
missing module named 'nbconvert.preprocessors' - imported by thinc.tests.test_examples (delayed)
missing module named nbformat - imported by thinc.tests.test_examples (delayed)
missing module named nbconvert - imported by thinc.tests.test_examples (delayed)
missing module named mypy - imported by thinc.mypy (top-level), thinc.tests.mypy.test_mypy (delayed)
missing module named ml_datasets - imported by spacy.cli.profile (delayed, conditional, optional), thinc.tests.layers.test_basic_tagger (delayed), thinc.tests.layers.test_mnist (delayed), thinc.tests.model.test_model (delayed)
missing module named mxnet - imported by thinc.compat (delayed), thinc.tests.layers.test_mxnet_wrapper (delayed)
missing module named pathy - imported by thinc.tests.conftest (delayed)
missing module named thinc_bigendian_ops - imported by thinc.backends (delayed, optional), thinc.tests.backends.test_ops (delayed, optional)
missing module named thinc_apple_ops - imported by thinc.backends.mps_ops (conditional, optional), thinc.backends (delayed, optional), thinc.tests.backends.test_ops (delayed, optional)
missing module named 'mypy.types' - imported by thinc.mypy (top-level)
missing module named 'mypy.subtypes' - imported by thinc.mypy (top-level)
missing module named 'mypy.plugin' - imported by thinc.mypy (top-level)
missing module named 'mypy.options' - imported by thinc.mypy (top-level)
missing module named 'mypy.nodes' - imported by thinc.mypy (top-level)
missing module named 'mypy.errors' - imported by thinc.mypy (top-level)
missing module named 'mypy.errorcodes' - imported by thinc.mypy (top-level)
missing module named os_signpost - imported by thinc.compat (optional)
missing module named 'cupy.cublas' - imported by thinc.compat (optional)
missing module named pep517 - imported by catalogue._importlib_metadata (delayed)
missing module named collections.Sized - imported by collections (conditional), srsly.ruamel_yaml.comments (conditional)
missing module named copy_reg - imported by srsly.ruamel_yaml.representer (conditional), spacy.compat (optional)
missing module named collections.Hashable - imported by collections (conditional), srsly.ruamel_yaml.compat (conditional)
missing module named _ruamel_yaml - imported by srsly.ruamel_yaml.main (optional)
missing module named spacy_pkuseg - imported by spacy.lang.zh (delayed, conditional, optional)
missing module named pyvi - imported by spacy.lang.vi (delayed, conditional, optional)
missing module named pymorphy3 - imported by spacy.lang.ru.lemmatizer (delayed, conditional, optional), spacy.lang.uk.lemmatizer (delayed, conditional, optional)
missing module named pymorphy2 - imported by spacy.lang.ru.lemmatizer (delayed, conditional, optional), spacy.lang.uk.lemmatizer (delayed, conditional, optional)
missing module named 'pythainlp.tokenize' - imported by spacy.lang.th (delayed, optional)
missing module named natto - imported by spacy.lang.ko (delayed, optional)
missing module named sudachipy - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional), spacy.lang.ja (delayed, optional)
missing module named mypy_boto3_s3 - imported by smart_open.s3 (conditional)
missing module named ot - imported by gensim.models.keyedvectors (delayed)
missing module named Pyro4 - imported by gensim.utils (delayed, optional), gensim.models.ldamodel (delayed, conditional, optional), gensim.models.lda_dispatcher (top-level), gensim.models.lda_worker (top-level), gensim.models.lsimodel (delayed, conditional, optional)
missing module named sparsesvd - imported by gensim.models.lsimodel (delayed, conditional, optional)
missing module named visdom - imported by gensim.models.callbacks (optional)
missing module named commands - imported by gensim.utils (delayed, optional)
missing module named cloudpathlib - imported by weasel.util.filesystem (delayed), weasel.util.remote (conditional), weasel.cli.remote_storage (conditional)
missing module named spacy_transformers - imported by spacy.cli.init_config (delayed, optional)
missing module named 'cupy.random' - imported by spacy.util (optional)
missing module named ftfy - imported by transformers.models.clip.tokenization_clip (delayed, optional), transformers.models.openai.tokenization_openai (delayed, optional)
missing module named 'flash_attn.ops' - imported by transformers.models.modernbert.modeling_modernbert (conditional)
missing module named 'flash_attn.flash_attn_interface' - imported by transformers.models.modernbert.modeling_modernbert (conditional)
missing module named 'mambapy.pscan' - imported by transformers.models.mamba.modeling_mamba (conditional)
missing module named torch.nn.SmoothL1Loss - imported by torch.nn (top-level), transformers.models.lxmert.modeling_lxmert (top-level)
missing module named 'detectron2.modeling' - imported by transformers.models.layoutlmv2.modeling_layoutlmv2 (conditional)
missing module named detectron2 - imported by transformers.models.layoutlmv2.configuration_layoutlmv2 (conditional), transformers.models.layoutlmv2.modeling_layoutlmv2 (conditional)
missing module named tensorflow_text - imported by transformers.models.bert.tokenization_bert_tf (top-level), transformers.models.bert_generation.modeling_bert_generation (delayed, optional), transformers.models.gpt2.tokenization_gpt2_tf (top-level)
missing module named keras_nlp - imported by transformers.models.gpt2.tokenization_gpt2_tf (top-level)
missing module named transformers.models.funnel.convert_funnel_original_tf_checkpoint_to_pytorch - imported by transformers.models.funnel (conditional)
missing module named g2p_en - imported by transformers.models.fastspeech2_conformer.tokenization_fastspeech2_conformer (delayed, optional)
missing module named selective_scan_cuda - imported by transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level)
missing module named causal_conv1d_cuda - imported by transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (optional)
missing module named einops - imported by transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level), torch._dynamo.decorators (delayed)
missing module named mambapy - imported by transformers.models.falcon_mamba.modeling_falcon_mamba (conditional)
missing module named natten - imported by transformers.models.dinat.modeling_dinat (conditional)
missing module named scann - imported by transformers.models.deprecated.realm.retrieval_realm (delayed)
missing module named 'pytorch_quantization.nn' - imported by transformers.models.deprecated.qdqbert.modeling_qdqbert (conditional, optional)
missing module named pytorch_quantization - imported by transformers.models.deprecated.qdqbert.modeling_qdqbert (conditional, optional)
missing module named xformers - imported by transformers.models.deprecated.open_llama.modeling_open_llama (optional)
missing module named 'natten.functional' - imported by transformers.models.deprecated.nat.modeling_nat (conditional)
missing module named emoji - imported by transformers.models.bertweet.tokenization_bertweet (delayed, optional)
missing module named rhoknp - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named unidic - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named unidic_lite - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named ipadic - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named fugashi - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named tensorflow_hub - imported by transformers.models.bert_generation.modeling_bert_generation (delayed, optional)
missing module named transformers.models.bamba.processing_bamba - imported by transformers.models.bamba (conditional)
missing module named spqr_quant - imported by transformers.integrations.spqr (delayed, conditional)
missing module named 'dvclive.utils' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'dvclive.plots' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named dvclive - imported by transformers.integrations.integration_utils (delayed)
missing module named flytekitplugins - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named flytekit - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named clearml - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named codecarbon - imported by transformers.integrations.integration_utils (delayed)
missing module named 'neptune.utils' - imported by transformers.integrations.integration_utils (delayed)
missing module named 'neptune.exceptions' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named 'neptune.new' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named 'neptune.internal' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named neptune - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named dagshub - imported by transformers.integrations.integration_utils (delayed)
missing module named mlflow - imported by transformers.integrations.integration_utils (delayed)
missing module named azureml - imported by transformers.integrations.integration_utils (delayed)
missing module named 'wandb.sdk' - imported by transformers.integrations.integration_utils (delayed)
missing module named sigopt - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'datasets.load' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'ray.tune' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named comet_ml - imported by transformers.integrations.integration_utils (delayed, conditional, optional)
missing module named fast_hadamard_transform - imported by transformers.integrations.higgs (conditional)
missing module named 'flute.tune' - imported by transformers.integrations.higgs (conditional)
missing module named 'flute.integrations' - imported by transformers.integrations.higgs (conditional)
missing module named fbgemm_gpu - imported by transformers.integrations.fbgemm_fp8 (conditional)
missing module named 'awq.quantize' - imported by transformers.integrations.awq (delayed, conditional)
missing module named 'awq.modules' - imported by transformers.integrations.awq (delayed, conditional)
missing module named awq - imported by transformers.integrations.awq (delayed)
missing module named aqlm - imported by transformers.integrations.aqlm (delayed)
missing module named torch.nn.BCELoss - imported by torch.nn (top-level), transformers.generation.watermarking (top-level)
missing module named markdownify - imported by transformers.agents.search (delayed, optional)
missing module named duckduckgo_search - imported by transformers.agents.search (delayed, optional)
missing module named 'gradio_client.utils' - imported by transformers.agents.tools (delayed)
missing module named gradio_client - imported by transformers.agents.tools (delayed)
missing module named soundfile - imported by transformers.agents.agent_types (conditional)
missing module named transformers.MaskFormerForInstanceSegmentationOutput - imported by transformers (conditional), transformers.models.maskformer.image_processing_maskformer (conditional)
missing module named 'torch._C._autograd' - imported by torch._subclasses.meta_utils (top-level), torch.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.autograd (top-level), torch.testing._internal.common_distributed (top-level)
missing module named 'numba.cuda' - imported by torch.testing._internal.common_cuda (conditional, optional)
missing module named torch.nn.Module - imported by torch.nn (top-level), torch._dynamo.mutation_guard (top-level), torch.optim.swa_utils (top-level), torch.jit._recursive (top-level), torch.jit._script (top-level), torch.jit._trace (top-level), torch.ao.quantization.fake_quantize (top-level), torch.fx.passes.utils.common (top-level), torch.distributed.nn.api.remote_module (top-level), torch.fx.experimental.proxy_tensor (top-level)
missing module named 'torch._C._onnx' - imported by torch.onnx (top-level), torch.onnx.symbolic_helper (top-level), torch.onnx.utils (top-level), torch.onnx._globals (top-level), torch.onnx.symbolic_opset9 (top-level), torch.onnx.symbolic_opset10 (top-level), torch.onnx.symbolic_opset13 (top-level), torch.onnx._experimental (top-level), torch.onnx.verification (top-level)
missing module named 'onnxscript.rewriter' - imported by torch.onnx._internal.onnxruntime (delayed, conditional, optional)
missing module named onnxscript - imported by torch.onnx._internal._lazy_import (conditional), torch.onnx._internal.exporter._core (top-level), torch.onnx._internal.exporter._dispatching (top-level), torch.onnx._internal.exporter._schemas (top-level), torch.onnx._internal.exporter._building (top-level), torch.onnx._internal.exporter._tensors (top-level), torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.registration (conditional), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.exporter._reporting (conditional), torch.onnx._internal._exporter_legacy (delayed, conditional, optional), torch.onnx._internal.fx.fx_onnx_interpreter (top-level), torch.onnx._internal.onnxruntime (delayed, conditional, optional)
missing module named 'onnxruntime.capi' - imported by torch.onnx._internal.onnxruntime (delayed, conditional)
missing module named 'onnx.defs' - imported by torch.onnx._internal.fx.type_utils (delayed, conditional)
missing module named 'onnxscript.function_libs' - imported by torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.exporter._ir_passes (delayed, optional), torch.onnx._internal.fx.decomposition_skip (top-level), torch.onnx._internal.fx.fx_onnx_interpreter (top-level)
missing module named 'onnxscript.ir' - imported by torch.onnx._internal.exporter._core (top-level), torch.onnx._internal.exporter._torchlib.ops.hop (delayed), torch.onnx._internal.exporter._building (top-level)
missing module named pyinstrument - imported by torch.onnx._internal.exporter._core (delayed, conditional)
missing module named 'onnxscript.evaluator' - imported by torch.onnx._internal.exporter._core (top-level)
missing module named 'onnxscript._framework_apis' - imported by torch.onnx._internal._lazy_import (conditional)
missing module named torch.multiprocessing._prctl_pr_set_pdeathsig - imported by torch.multiprocessing (top-level), torch.multiprocessing.spawn (top-level)
missing module named 'torch._C._monitor' - imported by torch.monitor (top-level)
missing module named 'torch._C._jit_tree_views' - imported by torch._sources (top-level), torch.jit.frontend (top-level)
missing module named torch.TensorType - imported by torch (top-level), torch.jit._passes._property_propagation (top-level)
missing module named 'monkeytype.tracing' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.db' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.config' - imported by torch.jit._monkeytype_config (optional)
missing module named monkeytype - imported by torch.jit._monkeytype_config (optional)
missing module named z3 - imported by torch.fx.experimental.validator (optional), torch.fx.experimental.migrate_gradual_types.transform_to_z3 (optional), torch.fx.experimental.migrate_gradual_types.z3_types (optional)
missing module named 'torch._C._distributed_rpc' - imported by torch.distributed.rpc (conditional), torch.distributed.rpc.api (top-level), torch.distributed.rpc.constants (top-level), torch.distributed.rpc.internal (top-level), torch.distributed.rpc.options (top-level), torch._jit_internal (conditional)
missing module named 'torch._C._distributed_rpc_testing' - imported by torch.distributed.rpc._testing (conditional)
missing module named torch.distributed.ReduceOp - imported by torch.distributed (top-level), torch.distributed.nn.functional (top-level)
missing module named torch.distributed.group - imported by torch.distributed (top-level), torch.distributed.nn.functional (top-level), torch.distributed.algorithms.model_averaging.utils (top-level)
missing module named torchdistx - imported by torch.distributed.fsdp._init_utils (optional)
missing module named etcd - imported by torch.distributed.elastic.rendezvous.etcd_rendezvous (top-level), torch.distributed.elastic.rendezvous.etcd_store (top-level), torch.distributed.elastic.rendezvous.etcd_rendezvous_backend (top-level), torch.distributed.elastic.rendezvous.etcd_server (optional)
missing module named 'torch.distributed.elastic.metrics.static_init' - imported by torch.distributed.elastic.metrics (optional)
missing module named 'torch._C._distributed_autograd' - imported by torch.distributed.autograd (conditional)
missing module named pulp - imported by torch.distributed._tools.sac_ilp (optional)
missing module named pwlf - imported by torch.distributed._tools.sac_estimator (delayed, optional)
missing module named amdsmi - imported by torch.cuda (conditional, optional), torch.cuda.memory (delayed, conditional, optional)
missing module named pynvml - imported by torch.cuda (delayed, conditional, optional), torch.cuda.memory (delayed, conditional, optional)
missing module named 'coremltools.models' - imported by torch.backends._coreml.preprocess (top-level)
missing module named 'coremltools.converters' - imported by torch.backends._coreml.preprocess (top-level)
missing module named coremltools - imported by torch.backends._coreml.preprocess (top-level)
missing module named torch.ao.quantization.QConfigAny - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.utils (top-level)
missing module named pytorch_lightning - imported by torch.ao.pruning._experimental.data_sparsifier.lightning.callbacks.data_sparsity (top-level)
missing module named torch.nn.ReLU - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Linear - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named 'torch._C._functorch' - imported by torch._functorch.pyfunctorch (top-level), torch._subclasses.fake_tensor (top-level), torch._subclasses.meta_utils (top-level), torch._functorch.autograd_function (top-level), torch._functorch.utils (top-level), torch._functorch.vmap (top-level), torch._functorch.eager_transforms (top-level), torch._higher_order_ops.cond (top-level)
missing module named torch._numpy.float_ - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.max - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isnan - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.signbit - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.real - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isscalar - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.iscomplexobj - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.imag - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.intp - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.empty - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.arange - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named fbscribelogger - imported by torch._logging.scribe (optional)
missing module named 'torch._C._lazy_ts_backend' - imported by torch._lazy.ts_backend (top-level), torch._lazy.computation (top-level)
missing module named 'torch._C._lazy' - imported by torch._lazy (top-level), torch._lazy.device_context (top-level), torch._lazy.metrics (top-level), torch._lazy.computation (top-level), torch._lazy.config (top-level), torch._lazy.debug (top-level), torch._lazy.ir_cache (top-level)
missing module named 'torch._inductor.fb' - imported by torch._inductor.remote_cache (delayed, conditional, optional), torch._inductor.cpp_builder (conditional), torch._inductor.graph (conditional), torch._functorch._aot_autograd.autograd_cache (delayed, optional), torch._inductor.compile_fx (conditional), torch._inductor.runtime.autotune_cache (delayed, optional), torch._inductor.codecache (conditional), torch._dynamo.pgo (delayed, optional), torch._inductor.utils (delayed, optional), torch._dynamo.utils (delayed, conditional, optional)
missing module named 'triton.testing' - imported by torch._inductor.runtime.benchmarking (delayed, optional), torch._inductor.utils (delayed)
missing module named 'ck4inductor.universal_gemm' - imported by torch._inductor.codegen.rocm.ck_universal_gemm_template (delayed, optional), torch._inductor.utils (delayed, optional)
missing module named ck4inductor - imported by torch._inductor.codegen.rocm.ck_universal_gemm_template (delayed, optional), torch._inductor.utils (delayed, optional), torch._inductor.codegen.rocm.ck_conv_template (optional)
missing module named halide - imported by torch._inductor.codecache (delayed, conditional), torch._inductor.runtime.halide_helpers (optional)
missing module named rfe - imported by torch._inductor.remote_cache (conditional)
missing module named redis - imported by torch._inductor.remote_cache (optional)
missing module named torch._inductor.fx_passes.fb - imported by torch._inductor.fx_passes (delayed, conditional), torch._inductor.fx_passes.pre_grad (delayed, conditional)
missing module named deeplearning - imported by torch._inductor.fx_passes.group_batch_fusion (optional)
missing module named 'triton.fb' - imported by torch._inductor.cpp_builder (conditional), torch._inductor.codecache (conditional)
missing module named 'libfb.py' - imported by torch._inductor.compile_worker.subproc_pool (delayed, conditional), torch._inductor.codegen.rocm.compile_command (delayed, conditional), torch._dynamo.debug_utils (conditional), torch._inductor.codecache (delayed, conditional)
missing module named 'ck4inductor.grouped_conv_fwd' - imported by torch._inductor.codegen.rocm.ck_conv_template (conditional)
missing module named 'cutlass_library.gemm_operation' - imported by torch._inductor.codegen.cuda.gemm_template (delayed), torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions (conditional)
missing module named 'cutlass_library.library' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional), torch._inductor.codegen.cuda.gemm_template (delayed), torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions (conditional)
missing module named 'cutlass_library.generator' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed)
missing module named 'cutlass_library.manifest' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named cutlass_library - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named 'triton._C' - imported by torch._higher_order_ops.triton_kernel_wrap (conditional)
missing module named 'torch_xla.stablehlo' - imported by torch._functorch.fx_minifier (delayed)
missing module named 'torch.utils._config_typing' - imported by torch._dynamo.config (conditional), torch._inductor.config (conditional), torch._functorch.config (conditional)
missing module named foo - imported by torch._functorch.compilers (delayed)
missing module named 'torch._C._dynamo' - imported by torch._dynamo.convert_frame (top-level), torch._dynamo.guards (top-level), torch._dynamo.eval_frame (top-level), torch._functorch._aot_autograd.input_output_analysis (top-level), torch._dynamo.decorators (conditional), torch._dynamo.types (top-level)
missing module named torchrec - imported by torch._dynamo.variables.user_defined (delayed)
missing module named torch._dynamo.variables.symbolic_convert - imported by torch._dynamo.variables.base (conditional)
missing module named 'einops._torch_specific' - imported by torch._dynamo.decorators (delayed, optional)
missing module named 'tvm.contrib' - imported by torch._dynamo.backends.tvm (delayed)
missing module named tvm - imported by torch._dynamo.backends.tvm (delayed, conditional)
missing module named com - imported by torch._appdirs (delayed)
missing module named libfb - imported by torch._inductor.config (conditional, optional)
missing module named 'torch._C._VariableFunctions' - imported by torch (conditional)
missing module named 'torch_xla.utils' - imported by torch._tensor (delayed, conditional)
missing module named torch.ScriptObject - imported by torch (delayed), torch.export.graph_signature (delayed)
missing module named torch.tensor - imported by torch (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), torch.utils.benchmark.utils.compare (top-level)
missing module named torch.trunc - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.square - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sqrt - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.signbit - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sign - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.round - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.reciprocal - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.rad2deg - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.negative - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.logical_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log1p - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log10 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isnan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isinf - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isfinite - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.floor - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.expm1 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.deg2rad - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.conj_physical - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.ceil - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.bitwise_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.absolute - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.true_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.subtract - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.remainder - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.pow - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.not_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.nextafter - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.multiply - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.minimum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.maximum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.ldexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.lcm - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.hypot - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.heaviside - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.gcd - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmod - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmin - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmax - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.floor_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.float_power - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.eq - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.copysign - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_right_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_left_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.arctan2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.add - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.broadcast_shapes - imported by torch (top-level), torch._numpy._funcs_impl (top-level)
missing module named torch._softmax_backward_data - imported by torch (delayed), transformers.pytorch_utils (delayed)
missing module named torch.norm_except_dim - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch._weight_norm - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch.randperm - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.Generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.default_generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.Size - imported by torch (top-level), torch.types (top-level), transformers.models.nemotron.modeling_nemotron (top-level), torch.nn.modules.normalization (top-level)
missing module named torch.qscheme - imported by torch (top-level), torch.types (top-level)
missing module named torch.layout - imported by torch (top-level), torch.types (top-level)
missing module named torch.DispatchKey - imported by torch (top-level), torch.types (top-level)
missing module named torch.device - imported by torch (top-level), torch.types (top-level), torch.nn.modules.module (top-level), torch._library.infer_schema (top-level), torch.cuda (top-level), torch.distributed.nn.api.remote_module (top-level), transformers.models.blip.modeling_blip_text (top-level), torch.xpu (top-level), torch._inductor.graph (top-level), torch.cpu (top-level), torch.mtia (top-level)
missing module named cupy - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), opt_einsum.backends.cupy (delayed), sklearn.utils._testing (delayed, conditional), srsly.msgpack._msgpack_numpy (optional), thinc.compat (optional), thinc.shims.pytorch (delayed, conditional), spacy.compat (optional), scipy._lib.array_api_compat.common._aliases (delayed, conditional), scipy._lib.array_api_compat.cupy (top-level), scipy._lib.array_api_compat.cupy._aliases (top-level), scipy._lib.array_api_compat.cupy.linalg (top-level), spacy.tests.test_misc (delayed, optional), thinc.tests.test_util (optional)
missing module named scipy.sparse.coo_array - imported by scipy.sparse (delayed, conditional), scipy.io._fast_matrix_market (delayed, conditional)
missing module named scipy.sparse.lil_matrix - imported by scipy.sparse (top-level), sklearn.manifold._locally_linear (top-level)
missing module named scipy.sparse.dia_matrix - imported by scipy.sparse (top-level), sklearn.cluster._bicluster (top-level)
missing module named scipy.sparse.csc_array - imported by scipy.sparse (top-level), scipy.optimize._milp (top-level)
missing module named scipy.sparse.csr_array - imported by scipy.sparse (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._ndbspline (top-level), sklearn.utils.fixes (optional)
missing module named scipy.sparse.vstack - imported by scipy.sparse (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._milp (top-level)
missing module named scipy.sparse.bmat - imported by scipy.sparse (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level)
missing module named scipy.sparse.find - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.common (top-level)
missing module named scipy.sparse.coo_matrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), pandas.core.arrays.sparse.accessor (delayed), sklearn.metrics._classification (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.common (top-level), scipy.io._mmio (top-level), scipy.io._fast_matrix_market (delayed, conditional), scipy.stats._crosstab (top-level)
missing module named scipy.sparse.diags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.spdiags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.dia_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.kron - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.sparray - imported by scipy.sparse (delayed), scipy.sparse._index (delayed), networkx.utils.backends (delayed), sklearn.utils.fixes (optional)
missing module named scipy.sparse.eye - imported by scipy.sparse (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level), scipy.optimize._trustregion_constr.equality_constrained_sqp (top-level), scipy.optimize._trustregion_constr.projections (top-level), sklearn.manifold._locally_linear (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.sparse.csr_matrix - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._lsq.lsq_linear (top-level), sklearn.utils._param_validation (top-level), sklearn.metrics.pairwise (top-level), sklearn.neighbors._base (top-level), sklearn.manifold._locally_linear (top-level), sklearn.manifold._t_sne (top-level), sklearn.metrics._classification (top-level), sklearn.metrics._ranking (top-level), scipy.sparse.csgraph._validation (top-level)
missing module named scipy.sparse.csc_matrix - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.linalg._sketches (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level), scipy.optimize._linprog_highs (top-level), sklearn.cluster._spectral (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.io._harwell_boeing.hb (top-level)
missing module named scipy.sparse.SparseEfficiencyWarning - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), sklearn.cluster._optics (top-level)
missing module named scipy.sparse.issparse - imported by scipy.sparse (top-level), scipy.sparse.linalg._interface (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._norm (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._constraints (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._differentialevolution (top-level), scipy.sparse.csgraph._laplacian (top-level), scipy.optimize._milp (top-level), sklearn.utils._param_validation (top-level), sklearn.externals._scipy.sparse.csgraph._laplacian (top-level), sklearn.utils._set_output (top-level), sklearn.utils.multiclass (top-level), sklearn.metrics.cluster._unsupervised (top-level), sklearn.metrics.pairwise (top-level), sklearn.metrics._pairwise_distances_reduction._dispatcher (top-level), sklearn.cluster._feature_agglomeration (top-level), sklearn.cluster._bicluster (top-level), sklearn.neighbors._base (top-level), sklearn.decomposition._pca (top-level), sklearn.cluster._hdbscan.hdbscan (top-level), sklearn.cluster._optics (top-level), sklearn.manifold._isomap (top-level), sklearn.manifold._t_sne (top-level), sklearn.metrics._classification (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), sklearn.metrics._ranking (top-level), sklearn.utils._indexing (top-level), scipy.io._mmio (top-level), tensorflow.python.keras.engine.data_adapter (delayed, optional), tensorflow.python.keras.engine.training_arrays_v1 (optional), tensorflow.python.keras.engine.training_v1 (optional), pandas.core.dtypes.common (delayed, conditional, optional), sklearn.tree._classes (top-level), scipy.sparse.csgraph._validation (top-level)
missing module named svgling - imported by nltk.tree.tree (delayed)
missing module named norm - imported by nltk.translate.gale_church (optional)
missing module named nltk.corpus.WordNetCorpusReader - imported by nltk.corpus (top-level), nltk.translate.meteor_score (top-level)
missing module named pycrfsuite - imported by nltk.tag.crf (optional)
missing module named 'bllipparser.RerankingParser' - imported by nltk.parse.bllip (optional)
missing module named bllipparser - imported by nltk.parse.bllip (optional)
missing module named nltk.induce_pcfg - imported by nltk (delayed), nltk.grammar (delayed)
missing module named nltk.Prover9 - imported by nltk (delayed), nltk.sem.glue (delayed)
missing module named nltk.word_tokenize - imported by nltk (delayed), nltk.classify.textcat (delayed)
missing module named nltk.FreqDist - imported by nltk (delayed), nltk.classify.textcat (delayed)
missing module named nltk.nonterminals - imported by nltk (delayed), nltk.parse.chart (delayed), nltk.grammar (delayed)
missing module named nltk.Tree - imported by nltk (delayed), nltk.tree.tree (delayed), nltk.chunk.regexp (delayed)
missing module named nltk.ProbabilisticTree - imported by nltk (delayed), nltk.tree.tree (delayed)
missing module named nltk.Production - imported by nltk (delayed), nltk.draw.cfg (delayed), nltk.parse.chart (delayed), nltk.grammar (delayed)
missing module named nltk.Nonterminal - imported by nltk (delayed), nltk.draw.cfg (delayed)
missing module named nltk.CFG - imported by nltk (delayed), nltk.draw.cfg (delayed), nltk.parse.chart (delayed), nltk.parse.recursivedescent (delayed), nltk.parse.shiftreduce (delayed), nltk.grammar (delayed)
missing module named numpypy - imported by nltk (optional)
missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
