@echo off
echo 修正版打包腳本...

REM 檢查PyInstaller
pyinstaller --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PyInstaller未安裝，正在安裝...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo ❌ 安裝失敗，請手動安裝: pip install pyinstaller
        pause
        exit /b 1
    )
)

REM 檢查main.py
if not exist main.py (
    echo ❌ main.py檔案不存在
    echo 當前目錄: %CD%
    dir *.py
    pause
    exit /b 1
)

echo ✅ 開始打包...

REM 清理舊檔案
if exist build rmdir /s /q build >nul 2>&1
if exist dist rmdir /s /q dist >nul 2>&1

REM 使用python -m方式執行（更可靠）
python -m PyInstaller ^
--onefile ^
--windowed ^
--optimize=2 ^
--strip ^
--exclude-module torch ^
--exclude-module tensorflow ^
--exclude-module sklearn ^
--exclude-module scipy ^
--exclude-module matplotlib ^
--name "CSV轉換器" ^
main.py

REM 檢查結果
if exist "dist\CSV轉換器.exe" (
    echo ✅ 打包成功！
    for %%I in ("dist\CSV轉換器.exe") do (
        set /a size_mb=%%~zI/1048576
        echo 檔案大小: %%~zI bytes (約 !size_mb! MB)
    )
    echo 輸出位置: %CD%\dist\CSV轉換器.exe
) else (
    echo ❌ 打包失敗
    echo 檢查錯誤信息...
)

pause
