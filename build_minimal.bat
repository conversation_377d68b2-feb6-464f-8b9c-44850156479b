@echo off
echo 開始最小化打包CSV轉換器...

REM 清理之前的建置檔案
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist

echo 使用優化命令打包...
pyinstaller ^
--onefile ^
--windowed ^
--optimize=2 ^
--strip ^
--upx-dir="C:\upx" ^
--exclude-module torch ^
--exclude-module tensorflow ^
--exclude-module sklearn ^
--exclude-module scipy ^
--exclude-module matplotlib ^
--exclude-module seaborn ^
--exclude-module plotly ^
--exclude-module fastapi ^
--exclude-module starlette ^
--exclude-module uvicorn ^
--exclude-module httpx ^
--exclude-module aiohttp ^
--exclude-module requests_oauthlib ^
--exclude-module astropy ^
--exclude-module dask ^
--exclude-module distributed ^
--exclude-module joblib ^
--exclude-module pytest ^
--exclude-module unittest ^
--exclude-module doctest ^
--exclude-module pdb ^
--exclude-module profile ^
--exclude-module cProfile ^
--exclude-module pstats ^
--exclude-module trace ^
--exclude-module tracemalloc ^
--exclude-module sphinx ^
--exclude-module docutils ^
--exclude-module markdown ^
--exclude-module jinja2 ^
--exclude-module IPython ^
--exclude-module jupyter ^
--exclude-module notebook ^
--exclude-module ipywidgets ^
--exclude-module babel ^
--exclude-module email.mime ^
--exclude-module lzma ^
--exclude-module bz2 ^
--exclude-module gzip ^
--exclude-module tarfile ^
--exclude-module cryptography ^
--exclude-module ssl ^
--exclude-module hashlib ^
--exclude-module PIL.ImageTk ^
--exclude-module PIL.ImageDraw ^
--exclude-module PIL.ImageFont ^
--exclude-module numpy.distutils ^
--exclude-module numpy.f2py ^
--exclude-module numpy.testing ^
--exclude-module pandas.plotting ^
--exclude-module pandas.io.formats.style ^
--exclude-module openpyxl.drawing ^
--exclude-module openpyxl.chart ^
--name "CSV轉換器" ^
--icon=icon.ico ^
main.py

if exist "dist\CSV轉換器.exe" (
    echo 打包成功！
    for %%I in ("dist\CSV轉換器.exe") do echo 檔案大小: %%~zI bytes (約 %%~zI/1048576 MB)
    
    REM 如果有UPX，進行額外壓縮
    where upx >nul 2>nul
    if %errorlevel% == 0 (
        echo 使用UPX進行額外壓縮...
        upx --best --lzma "dist\CSV轉換器.exe"
        for %%I in ("dist\CSV轉換器.exe") do echo 壓縮後大小: %%~zI bytes (約 %%~zI/1048576 MB)
    ) else (
        echo 提示：安裝UPX可進一步壓縮檔案大小
        echo 下載地址：https://upx.github.io/
    )
    
    echo 輸出檔案: dist\CSV轉換器.exe
) else (
    echo 打包失敗！
)

pause
