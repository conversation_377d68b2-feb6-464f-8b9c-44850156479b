@echo off
echo 開始優化打包CSV轉換器...

REM 清理之前的建置檔案
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist

echo 步驟1: 使用優化配置打包...
pyinstaller main_optimized.spec

if exist "dist\CSV轉換器_優化版.exe" (
    echo 步驟2: 檢查檔案大小...
    for %%I in ("dist\CSV轉換器_優化版.exe") do echo 檔案大小: %%~zI bytes
    
    echo 步驟3: 使用UPX進一步壓縮 (如果已安裝UPX)...
    where upx >nul 2>nul
    if %errorlevel% == 0 (
        upx --best "dist\CSV轉換器_優化版.exe"
        echo UPX壓縮完成
    ) else (
        echo UPX未安裝，跳過額外壓縮
        echo 可以從 https://upx.github.io/ 下載UPX來進一步壓縮
    )
    
    echo 打包完成！
    echo 輸出檔案: dist\CSV轉換器_優化版.exe
) else (
    echo 打包失敗！
)

pause
