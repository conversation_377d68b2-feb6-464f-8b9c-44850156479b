@echo off
echo 超級精簡打包...

REM 清理
if exist build rmdir /s /q build >nul 2>&1
if exist dist rmdir /s /q dist >nul 2>&1

REM 超級精簡打包（無調試信息）
pyinstaller --onefile --windowed --optimize=2 --strip --noupx --clean ^
--exclude-module torch --exclude-module tensorflow --exclude-module sklearn ^
--exclude-module scipy --exclude-module matplotlib --exclude-module numpy.distutils ^
--exclude-module pandas.plotting --exclude-module openpyxl.drawing ^
--exclude-module fastapi --exclude-module aiohttp --exclude-module jupyter ^
--exclude-module IPython --exclude-module pytest --exclude-module sphinx ^
--name "CSV轉換器_精簡版" main.py >nul 2>&1

if exist "dist\CSV轉換器_精簡版.exe" (
    for %%I in ("dist\CSV轉換器_精簡版.exe") do echo 成功！大小: %%~zI/1048576 MB
) else (
    echo 失敗！
)

pause
