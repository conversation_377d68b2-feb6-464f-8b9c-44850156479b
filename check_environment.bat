@echo off
echo 檢查Python環境和PyInstaller...

echo.
echo 1. 檢查Python版本:
python --version
if %errorlevel% neq 0 (
    echo ❌ Python未安裝或未加入PATH
    goto :end
)

echo.
echo 2. 檢查pip版本:
pip --version
if %errorlevel% neq 0 (
    echo ❌ pip未安裝或未加入PATH
    goto :end
)

echo.
echo 3. 檢查PyInstaller:
pyinstaller --version
if %errorlevel% neq 0 (
    echo ❌ PyInstaller未安裝
    echo 正在安裝PyInstaller...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo ❌ PyInstaller安裝失敗
        goto :end
    )
    echo ✅ PyInstaller安裝成功
) else (
    echo ✅ PyInstaller已安裝
)

echo.
echo 4. 檢查必要套件:
echo 檢查pandas...
python -c "import pandas; print('✅ pandas版本:', pandas.__version__)" 2>nul || echo "❌ pandas未安裝"

echo 檢查openpyxl...
python -c "import openpyxl; print('✅ openpyxl版本:', openpyxl.__version__)" 2>nul || echo "❌ openpyxl未安裝"

echo 檢查tkinter...
python -c "import tkinter; print('✅ tkinter可用')" 2>nul || echo "❌ tkinter未安裝"

echo.
echo 5. 檢查main.py檔案:
if exist main.py (
    echo ✅ main.py檔案存在
) else (
    echo ❌ main.py檔案不存在
)

echo.
echo 6. 檢查當前目錄:
echo 當前目錄: %CD%
dir main.py 2>nul || echo "❌ 當前目錄沒有main.py"

:end
echo.
echo 檢查完成！
pause
