#!/usr/bin/env python3
"""
應用程式設定檔
"""

import logging
from pathlib import Path

# 應用程式基本設定
APP_NAME = "CSV轉換器"
APP_VERSION = "2.0.0"
WINDOW_SIZE = "600x750"

# 檔案設定
SUPPORTED_FILE_TYPES = [
    ("CSV/SPD/ZIP files", "*.csv;*.spd;*.zip"),
    ("CSV files", "*.csv"),
    ("SPD files", "*.spd"),
    ("ZIP files", "*.zip"),
    ("All files", "*.*")
]
OUTPUT_SUFFIX_FULL = "_clean_converted.xlsx"
OUTPUT_SUFFIX_DATA11 = "_data11_formatted.csv"
OUTPUT_SUFFIX_EXACT = "_exact_data11.csv"
OUTPUT_SUFFIX_SPD_FULL = "_spd_converted.xlsx"
OUTPUT_SUFFIX_SPD_DATA11 = "_spd_data11.csv"

# 日誌設定
LOG_LEVEL = logging.INFO
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'
LOG_FILE = 'conversion.log'

# 轉換器設定
class TesterType:
    UNKNOWN = 0
    CTA8280 = 1
    OTHER = 2
    TMT = 3  # TMT格式（A1包含Spreadsheet）

# CTA8280格式設定
CTA8280_HEADERS = {
    'A1': 'Spreadsheet',
    'B1': 'Format',
    'A2': 'Test Program:',
    'A3': 'Lot ID:',
    'A4': 'Operator:',
    'A5': 'Computer:',
    'A6': 'Date:',
    'A9': 'CTA',
    'A12': 'Serial#',
    'B12': 'Bin#'
}

# 資料標記
DATA_MARKERS = {
    'DATA': '[Data]',
    'QA_DATA': '[QAData]'
}

# 搜尋關鍵字
SEARCH_KEYWORDS = {
    'INDEX_NO': 'Index_No',
    'DATA_NUM': ['Data_Num', 'Data_Cnt'],
    'TEST_NUM': 'TEST_NUM',
    'SW_BIN': 'SW_Bin',
    'PROGRAM_NAME': 'ProgramName',
    'OPERATOR': 'Operator',
    'END_TIME': 'EndTime',
    'LOT_ID': 'LotID',
    'COMPUTER': 'Computer'
}

# 處理限制
PROCESSING_LIMITS = {
    'MAX_COLUMNS': 300,
    'MAX_ROWS': 65536,
    'MAX_DEVICE_ROWS': 1000,
    'HEADER_SEARCH_ROWS': 10,
    'SUM_INFO_ROWS': 100,
    'SUM_INFO_COLS': 10
}

# FillEmptyItemName設定
FILL_EMPTY_SETTINGS = {
    'DEFAULT_MAX_VALUE': 'none',
    'DEFAULT_MIN_VALUE': 'none',
    'TEST_TIME_NAME': 'Test_Time',
    'FONT_COLOR_RED': 'FF0000',
    'ITEM_NUMBER_FORMAT': '0.{:02d}.{:02d}',
    'SIMPLE_FORMAT': '0.00.{:02d}'
}
