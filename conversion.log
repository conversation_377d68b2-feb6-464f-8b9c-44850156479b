2025-07-24 17:10:11,214 - INFO - 開始完整轉換...
2025-07-24 17:10:11,215 - INFO - 讀取CSV文件: C:/Users/<USER>/Desktop/test/G5440WC(CC)_GHBR04.1_02.spd
2025-07-24 17:10:11,277 - INFO - 成功讀取CSV文件，大小: (911, 110)
2025-07-24 17:10:11,279 - INFO - 檢測到TMT測試儀格式（通過A1=Spreadsheet）
2025-07-24 17:10:11,279 - INFO - === 開始7步驟統一處理管道 ===
2025-07-24 17:10:11,280 - INFO - 步驟4: 檢測到TMT格式，直接進入步驟6
2025-07-24 17:10:11,280 - INFO - 創建TMT格式工作簿...
2025-07-24 17:10:11,947 - INFO - ⚡ 優化寫入完成: 911行 × 110列，耗時664.0ms
2025-07-24 17:10:11,948 - INFO - 步驟5：在9A位置標記原始格式為TMT...
2025-07-24 17:10:11,949 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-24 17:10:11,950 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-24 17:10:11,950 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-24 17:10:11,950 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-24 17:10:11,950 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-24 17:10:11,950 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-24 17:10:11,951 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-24 17:10:11,951 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-24 17:10:11,951 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-24 17:10:11,951 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-24 17:10:11,951 - INFO - 🔍 開始統一數據收集...
2025-07-24 17:10:11,951 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-24 17:10:11,978 - INFO - 收集項目數據: 108個項目
2025-07-24 17:10:11,979 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-24 17:10:35,232 - INFO - 收集設備數據: 899個設備（已排除無測試數據的行）
2025-07-24 17:10:35,232 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-24 17:10:35,260 - INFO - 找到Site列: 第8行第4列，標題: Site
2025-07-24 17:10:35,263 - INFO - 收集Site數據: 2個Site，899個設備有Site信息
2025-07-24 17:10:35,263 - INFO - 🔄 6.1.4 收集限制值...
2025-07-24 17:10:35,264 - INFO - 收集限制值: Max 108個, Min 108個
2025-07-24 17:10:35,264 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-24 17:10:35,265 - INFO - 設置第6行項目編號: 從第3列開始，共108個項目
2025-07-24 17:10:35,265 - INFO - ⏱️ 統一數據收集 執行時間: 23.31s
2025-07-24 17:10:35,265 - INFO - ✅ 統一數據收集完成: 項目108個, 設備899個, Site2個
2025-07-24 17:10:35,265 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 23.31s
2025-07-24 17:10:35,265 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-24 17:10:35,266 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-24 17:10:35,266 - INFO -   - 填充缺失的項目編號和名稱
2025-07-24 17:10:35,266 - INFO -   - 設置紅色字體標記
2025-07-24 17:10:35,267 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 2.0ms
2025-07-24 17:10:35,267 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-24 17:10:35,267 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-24 17:10:35,267 - INFO -   - 填充預設限制值
2025-07-24 17:10:35,268 - INFO -   - 設置紅色字體標記
2025-07-24 17:10:35,268 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: <1ms
2025-07-24 17:10:35,268 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-24 17:10:35,268 - INFO -   - 驗證數據完整性
2025-07-24 17:10:35,269 - INFO -   - 準備統一數據摘要
2025-07-24 17:10:35,269 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 23.32s
2025-07-24 17:10:35,269 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-24 17:10:35,269 - INFO - 應用Device2BinControl處理...
2025-07-24 17:10:35,269 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-24 17:10:35,269 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-24 17:10:35,269 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-24 17:10:35,269 - INFO - 步驟7：讀取步驟5設定的原始格式標記: TMT
2025-07-24 17:10:35,270 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: 1.0ms
2025-07-24 17:10:35,270 - INFO - ✅ 步驟2：原始格式類型: TMT
2025-07-24 17:10:35,271 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-24 17:10:35,328 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-24 17:10:35,329 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 58.0ms
2025-07-24 17:10:35,329 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-24 17:10:35,330 - INFO - ✅ 數據摘要: 項目108個, 設備899個, Site2個
2025-07-24 17:10:35,330 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: 1.0ms
2025-07-24 17:10:35,330 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-24 17:10:35,330 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 17:10:35,331 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 17:10:35,331 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 17:10:35,331 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 17:10:35,332 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-24 17:10:35,332 - INFO - 使用統一數據: 設備899個, 項目108個
2025-07-24 17:10:35,333 - INFO - 收集原始Bin值: 899個設備
2025-07-24 17:10:35,988 - INFO - 應用染色邏輯: 72個設備的失敗項目
2025-07-24 17:10:35,990 - INFO - Bin統計: Pass=827, Fail=72, Total=899
2025-07-24 17:10:35,991 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-24 17:10:35,991 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-24 17:10:35,991 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 661.0ms
2025-07-24 17:10:35,992 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-24 17:10:35,992 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 17:10:35,992 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 17:10:35,992 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 17:10:35,992 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 17:10:35,993 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 17:10:35,993 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 17:10:39,238 - INFO - ⚡ 超級批量字體設置完成，總共處理97888個cell
2025-07-24 17:10:39,238 - INFO - 設置數據區域字體顏色: 行13-911, 列2-110
2025-07-24 17:10:39,239 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 3.25s
2025-07-24 17:10:39,239 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 3.97s
2025-07-24 17:10:39,240 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-24 17:10:39,240 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-24 17:10:39,240 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-24 17:10:39,240 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 17:10:39,241 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 17:10:39,241 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 17:10:39,241 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 17:10:39,242 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-24 17:10:39,242 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 17:10:39,242 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 17:10:39,242 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 17:10:39,243 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 17:10:39,243 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 17:10:39,243 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 17:10:39,243 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-24 17:10:39,243 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-24 17:10:39,244 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-24 17:10:39,244 - INFO - 步驟7：讀取步驟5設定的原始格式標記: TMT
2025-07-24 17:10:39,244 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-24 17:10:39,244 - INFO - ✅ 步驟2：原始格式類型: TMT
2025-07-24 17:10:39,245 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-24 17:10:39,300 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-24 17:10:39,301 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 56.0ms
2025-07-24 17:10:39,302 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-24 17:10:39,302 - INFO - ✅ 數據摘要: 項目108個, 設備899個, Site2個
2025-07-24 17:10:39,303 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: 2.0ms
2025-07-24 17:10:39,303 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-24 17:10:39,303 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 17:10:39,304 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 17:10:39,304 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 17:10:39,304 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 17:10:39,305 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-24 17:10:39,305 - INFO - 使用統一數據: 設備899個, 項目108個
2025-07-24 17:10:39,307 - INFO - 收集原始Bin值: 899個設備
2025-07-24 17:10:39,960 - INFO - 應用染色邏輯: 72個設備的失敗項目
2025-07-24 17:10:39,962 - INFO - Bin統計: Pass=827, Fail=72, Total=899
2025-07-24 17:10:39,963 - INFO - VBA 457-469行：填充基本統計 Total=899, Pass=827, Fail=72, Yield=91.99%
2025-07-24 17:10:39,965 - INFO - 添加原始檔案超連結: C:/Users/<USER>/Desktop/test/G5440WC(CC)_GHBR04.1_02.spd
2025-07-24 17:10:39,967 - INFO - 計算實際第4列(Definition列)項目數量: 0
2025-07-24 17:10:39,967 - INFO - Site信息檢查: total_site_no=2, good_site_n=True, site_data數量=899
2025-07-24 17:10:39,968 - INFO - 創建Site統計完成: 2個Site
2025-07-24 17:10:39,969 - INFO -   Site 1: 439個設備
2025-07-24 17:10:39,969 - INFO -   Site 2: 460個設備
2025-07-24 17:10:39,969 - INFO - 開始填充Site統計: 2個Site, 實際項目數量: 0
2025-07-24 17:10:39,969 - INFO - 填充Site 1統計完成: 439個設備
2025-07-24 17:10:39,970 - INFO - 填充Site 2統計完成: 460個設備
2025-07-24 17:10:39,970 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 0
2025-07-24 17:10:39,998 - INFO - VBA 362-420行（修正版）：填充了6個測試項目的Bin數據，無重複
2025-07-24 17:10:39,999 - INFO - 設置 AutoFilter 範圍: A6:I12
2025-07-24 17:10:40,000 - INFO - 按 B 欄由大到小排序了 6 行資料
2025-07-24 17:10:40,001 - INFO - 自動調整D列寬度: 16.4 (基於最大內容長度: 12)
2025-07-24 17:10:40,001 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-24 17:10:40,001 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-24 17:10:40,002 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 699.1ms
2025-07-24 17:10:40,002 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-24 17:10:40,002 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 17:10:40,003 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 17:10:40,003 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 17:10:40,003 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 17:10:40,004 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 17:10:40,004 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 17:10:44,951 - INFO - ⚡ 超級批量字體設置完成，總共處理97888個cell
2025-07-24 17:10:44,952 - INFO - 設置數據區域字體顏色: 行13-911, 列2-110
2025-07-24 17:10:44,952 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 4.95s
2025-07-24 17:10:44,953 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 5.71s
2025-07-24 17:10:44,953 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-24 17:10:44,953 - INFO - === 統一處理管道完成 ===
2025-07-24 17:10:53,618 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-24 17:10:55,122 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\G5440WC(CC)_GHBR04.1_02_spd_converted.xlsx，耗時1502.4ms
2025-07-24 17:10:55,122 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\G5440WC(CC)_GHBR04.1_02_spd_converted.xlsx
2025-07-24 17:10:55,125 - INFO - 開始完整轉換...
2025-07-24 17:10:55,126 - INFO - 讀取CSV文件: C:/Users/<USER>/Desktop/test/GMT_G2304.csv
2025-07-24 17:10:55,160 - INFO - 成功讀取CSV文件，大小: (3511, 60)
2025-07-24 17:10:55,169 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-24 17:10:55,170 - INFO - === 開始7步驟統一處理管道 ===
2025-07-24 17:10:55,170 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-24 17:10:55,171 - INFO - 執行CTA到TMT格式轉換...
2025-07-24 17:10:57,088 - INFO - ⚡ 優化寫入完成: 3484行 × 60列，耗時1905.2ms
2025-07-24 17:10:57,104 - INFO - ⚡ 優化寫入完成: 22行 × 60列，耗時14.9ms
2025-07-24 17:10:57,110 - INFO - ⚡ 優化寫入完成: 4行 × 60列，耗時3.0ms
2025-07-24 17:10:57,111 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時1930.8ms
2025-07-24 17:10:57,111 - INFO - 應用CTA8280轉換處理...
2025-07-24 17:10:57,112 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-24 17:10:57,112 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-24 17:10:57,113 - INFO - 找到標題行在第2行
2025-07-24 17:10:57,113 - INFO - ⏱️ 步驟1-找到標題行 執行時間: <1ms
2025-07-24 17:10:57,113 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-24 17:10:57,114 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: <1ms
2025-07-24 17:10:57,114 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-24 17:10:57,116 - INFO - myFindedRowN=2, myColumnN=14
2025-07-24 17:10:57,155 - INFO - ⚡ 超級批量插入6行完成，耗時7.0ms
2025-07-24 17:10:57,155 - INFO - 插入了6行在最前面
2025-07-24 17:10:57,156 - INFO - ⚡ 標題行插入優化完成，耗時7.0ms
2025-07-24 17:10:57,157 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 43.0ms
2025-07-24 17:10:57,158 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-24 17:10:57,160 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-24 17:10:57,161 - INFO - 清空位置 A8: "Index_No"
2025-07-24 17:10:57,162 - INFO - 清空位置 B8: "Dut_No"
2025-07-24 17:10:57,163 - INFO - 清空位置 A10: "ASD"
2025-07-24 17:10:57,163 - INFO - 清空位置 B10: "QQ"
2025-07-24 17:10:57,164 - INFO - 清空位置 A11: "A"
2025-07-24 17:10:57,164 - INFO - 清空位置 B11: "B"
2025-07-24 17:10:57,165 - INFO - ✅ 清空了6個多餘資料位置
2025-07-24 17:10:57,166 - INFO - 最後一列資料密度正常(31.0%)，保留資料
2025-07-24 17:10:57,167 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 9.0ms
2025-07-24 17:10:57,168 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-24 17:10:57,170 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: 2.0ms
2025-07-24 17:10:57,170 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-24 17:10:57,172 - INFO - 找到SW_Bin列在第6列
2025-07-24 17:10:57,173 - INFO - 處理了17個設備的數據
2025-07-24 17:10:57,174 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: 4.0ms
2025-07-24 17:10:57,174 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 62.0ms
2025-07-24 17:10:57,175 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-24 17:10:57,175 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-24 17:10:57,175 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-24 17:10:57,176 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-24 17:10:57,176 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-24 17:10:57,176 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-24 17:10:57,176 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-24 17:10:57,177 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-24 17:10:57,177 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-24 17:10:57,177 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-24 17:10:57,178 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-24 17:10:57,178 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-24 17:10:57,178 - INFO - 🔍 開始統一數據收集...
2025-07-24 17:10:57,179 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-24 17:10:57,180 - INFO - 收集項目數據: 58個項目
2025-07-24 17:10:57,180 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-24 17:10:57,190 - INFO - 收集設備數據: 17個設備（已排除無測試數據的行）
2025-07-24 17:10:57,190 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-24 17:10:57,192 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-24 17:10:57,192 - INFO - 收集Site數據: 4個Site，16個設備有Site信息
2025-07-24 17:10:57,193 - INFO - 🔄 6.1.4 收集限制值...
2025-07-24 17:10:57,194 - INFO - 收集限制值: Max 45個, Min 45個
2025-07-24 17:10:57,194 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-24 17:10:57,195 - INFO - 設置第6行項目編號: 從第3列開始，共58個項目
2025-07-24 17:10:57,196 - INFO - ⏱️ 統一數據收集 執行時間: 18.0ms
2025-07-24 17:10:57,196 - INFO - ✅ 統一數據收集完成: 項目58個, 設備17個, Site4個
2025-07-24 17:10:57,197 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 19.0ms
2025-07-24 17:10:57,197 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-24 17:10:57,197 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-24 17:10:57,198 - INFO -   - 填充缺失的項目編號和名稱
2025-07-24 17:10:57,198 - INFO -   - 設置紅色字體標記
2025-07-24 17:10:57,202 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 5.0ms
2025-07-24 17:10:57,202 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-24 17:10:57,203 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-24 17:10:57,203 - INFO -   - 填充預設限制值
2025-07-24 17:10:57,204 - INFO -   - 設置紅色字體標記
2025-07-24 17:10:57,210 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 7.0ms
2025-07-24 17:10:57,210 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-24 17:10:57,211 - INFO -   - 驗證數據完整性
2025-07-24 17:10:57,212 - INFO -   - 準備統一數據摘要
2025-07-24 17:10:57,213 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 35.0ms
2025-07-24 17:10:57,213 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-24 17:10:57,214 - INFO - 應用Device2BinControl處理...
2025-07-24 17:10:57,214 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-24 17:10:57,214 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-24 17:10:57,215 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-24 17:10:57,215 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-24 17:10:57,215 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-24 17:10:57,216 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-24 17:10:57,216 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-24 17:10:57,218 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-24 17:10:57,219 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 3.0ms
2025-07-24 17:10:57,219 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-24 17:10:57,220 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-24 17:10:57,220 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: 1.0ms
2025-07-24 17:10:57,220 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-24 17:10:57,221 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 17:10:57,221 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 17:10:57,222 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 17:10:57,222 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 17:10:57,223 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-24 17:10:57,223 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-24 17:10:57,224 - INFO - 收集原始Bin值: 17個設備
2025-07-24 17:10:57,236 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-24 17:10:57,237 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-24 17:10:57,238 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-24 17:10:57,238 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-24 17:10:57,239 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 19.0ms
2025-07-24 17:10:57,240 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-24 17:10:57,240 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 17:10:57,241 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 17:10:57,241 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 17:10:57,241 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 17:10:57,242 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 17:10:57,242 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 17:10:57,310 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-24 17:10:57,311 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-24 17:10:57,311 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 72.0ms
2025-07-24 17:10:57,312 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 98.0ms
2025-07-24 17:10:57,312 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-24 17:10:57,312 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-24 17:10:57,313 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-24 17:10:57,313 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 17:10:57,313 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 17:10:57,314 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 17:10:57,314 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 17:10:57,314 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-24 17:10:57,315 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 17:10:57,315 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 17:10:57,315 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 17:10:57,315 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 17:10:57,316 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 17:10:57,316 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 17:10:57,316 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-24 17:10:57,317 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: 1.0ms
2025-07-24 17:10:57,317 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-24 17:10:57,318 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-24 17:10:57,318 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-24 17:10:57,318 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-24 17:10:57,319 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-24 17:10:57,320 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-24 17:10:57,321 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 3.0ms
2025-07-24 17:10:57,321 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-24 17:10:57,322 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-24 17:10:57,322 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-24 17:10:57,323 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-24 17:10:57,323 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 17:10:57,324 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 17:10:57,324 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 17:10:57,324 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 17:10:57,325 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-24 17:10:57,325 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-24 17:10:57,325 - INFO - 收集原始Bin值: 17個設備
2025-07-24 17:10:57,336 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-24 17:10:57,337 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-24 17:10:57,338 - INFO - VBA 457-469行：填充基本統計 Total=17, Pass=5, Fail=12, Yield=29.41%
2025-07-24 17:10:57,339 - INFO - 添加原始檔案超連結: C:/Users/<USER>/Desktop/test/GMT_G2304.csv
2025-07-24 17:10:57,340 - INFO - 計算實際第4列(Definition列)項目數量: 0
2025-07-24 17:10:57,340 - INFO - Site信息檢查: total_site_no=4, good_site_n=True, site_data數量=16
2025-07-24 17:10:57,341 - INFO - 創建Site統計完成: 4個Site
2025-07-24 17:10:57,342 - INFO -   Site 1: 4個設備
2025-07-24 17:10:57,342 - INFO -   Site 2: 4個設備
2025-07-24 17:10:57,342 - INFO -   Site 3: 4個設備
2025-07-24 17:10:57,343 - INFO -   Site 4: 4個設備
2025-07-24 17:10:57,343 - INFO - 開始填充Site統計: 4個Site, 實際項目數量: 0
2025-07-24 17:10:57,344 - INFO - 填充Site 1統計完成: 4個設備
2025-07-24 17:10:57,345 - INFO - 填充Site 2統計完成: 4個設備
2025-07-24 17:10:57,345 - INFO - 填充Site 3統計完成: 4個設備
2025-07-24 17:10:57,346 - INFO - 填充Site 4統計完成: 4個設備
2025-07-24 17:10:57,346 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 0
2025-07-24 17:10:57,347 - INFO - VBA 362-420行（修正版）：填充了2個測試項目的Bin數據，無重複
2025-07-24 17:10:57,348 - INFO - 設置 AutoFilter 範圍: A6:M8
2025-07-24 17:10:57,348 - INFO - 按 B 欄由大到小排序了 2 行資料
2025-07-24 17:10:57,349 - INFO - 自動調整D列寬度: 14.0 (基於最大內容長度: 10)
2025-07-24 17:10:57,349 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-24 17:10:57,350 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-24 17:10:57,350 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 27.0ms
2025-07-24 17:10:57,350 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-24 17:10:57,351 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 17:10:57,351 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 17:10:57,352 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 17:10:57,352 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 17:10:57,352 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 17:10:57,353 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 17:10:57,442 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-24 17:10:57,442 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-24 17:10:57,443 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 93.0ms
2025-07-24 17:10:57,443 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 127.0ms
2025-07-24 17:10:57,443 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-24 17:10:57,444 - INFO - === 統一處理管道完成 ===
2025-07-24 17:11:01,993 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-24 17:11:02,521 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\GMT_G2304_clean_converted.xlsx，耗時528.0ms
2025-07-24 17:11:02,522 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\GMT_G2304_clean_converted.xlsx
