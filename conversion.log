2025-07-24 15:11:27,762 - INFO - 開始完整轉換...
2025-07-24 15:11:27,762 - INFO - 讀取CSV文件: C:/Users/<USER>/Desktop/test/GMT_G2304.csv
2025-07-24 15:11:27,788 - INFO - 成功讀取CSV文件，大小: (3511, 60)
2025-07-24 15:11:27,793 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-24 15:11:27,794 - INFO - === 開始7步驟統一處理管道 ===
2025-07-24 15:11:27,794 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-24 15:11:27,794 - INFO - 執行CTA到TMT格式轉換...
2025-07-24 15:11:29,027 - INFO - ⚡ 優化寫入完成: 3484行 × 60列，耗時1225.0ms
2025-07-24 15:11:29,037 - INFO - ⚡ 優化寫入完成: 22行 × 60列，耗時9.0ms
2025-07-24 15:11:29,039 - INFO - ⚡ 優化寫入完成: 4行 × 60列，耗時1.0ms
2025-07-24 15:11:29,040 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時1241.0ms
2025-07-24 15:11:29,040 - INFO - 應用CTA8280轉換處理...
2025-07-24 15:11:29,040 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-24 15:11:29,040 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-24 15:11:29,041 - INFO - 找到標題行在第2行
2025-07-24 15:11:29,041 - INFO - ⏱️ 步驟1-找到標題行 執行時間: 1.0ms
2025-07-24 15:11:29,041 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-24 15:11:29,042 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: 1.0ms
2025-07-24 15:11:29,042 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-24 15:11:29,045 - INFO - myFindedRowN=2, myColumnN=14
2025-07-24 15:11:29,071 - INFO - ⚡ 超級批量插入6行完成，耗時5.0ms
2025-07-24 15:11:29,071 - INFO - 插入了6行在最前面
2025-07-24 15:11:29,072 - INFO - ⚡ 標題行插入優化完成，耗時5.0ms
2025-07-24 15:11:29,072 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 30.0ms
2025-07-24 15:11:29,072 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-24 15:11:29,073 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-24 15:11:29,073 - INFO - 清空位置 A8: "Index_No"
2025-07-24 15:11:29,073 - INFO - 清空位置 B8: "Dut_No"
2025-07-24 15:11:29,073 - INFO - 清空位置 A10: "ASD"
2025-07-24 15:11:29,074 - INFO - 清空位置 B10: "QQ"
2025-07-24 15:11:29,074 - INFO - 清空位置 A11: "A"
2025-07-24 15:11:29,074 - INFO - 清空位置 B11: "B"
2025-07-24 15:11:29,074 - INFO - ✅ 清空了6個多餘資料位置
2025-07-24 15:11:29,075 - INFO - 最後一列資料密度正常(31.0%)，保留資料
2025-07-24 15:11:29,076 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 4.0ms
2025-07-24 15:11:29,076 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-24 15:11:29,077 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: 1.0ms
2025-07-24 15:11:29,077 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-24 15:11:29,078 - INFO - 找到SW_Bin列在第6列
2025-07-24 15:11:29,078 - INFO - 處理了17個設備的數據
2025-07-24 15:11:29,078 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: <1ms
2025-07-24 15:11:29,079 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 39.0ms
2025-07-24 15:11:29,079 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-24 15:11:29,079 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-24 15:11:29,079 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-24 15:11:29,079 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-24 15:11:29,079 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-24 15:11:29,079 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-24 15:11:29,079 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-24 15:11:29,080 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-24 15:11:29,080 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-24 15:11:29,080 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-24 15:11:29,081 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-24 15:11:29,081 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-24 15:11:29,081 - INFO - 🔍 開始統一數據收集...
2025-07-24 15:11:29,081 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-24 15:11:29,082 - INFO - 收集項目數據: 58個項目
2025-07-24 15:11:29,083 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-24 15:11:29,090 - INFO - 收集設備數據: 17個設備（已排除無測試數據的行）
2025-07-24 15:11:29,090 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-24 15:11:29,091 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-24 15:11:29,091 - INFO - 收集Site數據: 4個Site，16個設備有Site信息
2025-07-24 15:11:29,091 - INFO - 🔄 6.1.4 收集限制值...
2025-07-24 15:11:29,092 - INFO - 收集限制值: Max 45個, Min 45個
2025-07-24 15:11:29,092 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-24 15:11:29,092 - INFO - 設置第6行項目編號: 從第3列開始，共58個項目
2025-07-24 15:11:29,093 - INFO - ⏱️ 統一數據收集 執行時間: 12.0ms
2025-07-24 15:11:29,093 - INFO - ✅ 統一數據收集完成: 項目58個, 設備17個, Site4個
2025-07-24 15:11:29,093 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 12.0ms
2025-07-24 15:11:29,094 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-24 15:11:29,094 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-24 15:11:29,094 - INFO -   - 填充缺失的項目編號和名稱
2025-07-24 15:11:29,094 - INFO -   - 設置紅色字體標記
2025-07-24 15:11:29,097 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 3.0ms
2025-07-24 15:11:29,098 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-24 15:11:29,098 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-24 15:11:29,099 - INFO -   - 填充預設限制值
2025-07-24 15:11:29,099 - INFO -   - 設置紅色字體標記
2025-07-24 15:11:29,103 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 5.0ms
2025-07-24 15:11:29,104 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-24 15:11:29,104 - INFO -   - 驗證數據完整性
2025-07-24 15:11:29,105 - INFO -   - 準備統一數據摘要
2025-07-24 15:11:29,106 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 24.0ms
2025-07-24 15:11:29,106 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-24 15:11:29,107 - INFO - 應用Device2BinControl處理...
2025-07-24 15:11:29,108 - INFO - 🚀 步驟6-7：開始數據分析和染色處理...
2025-07-24 15:11:29,108 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-24 15:11:29,109 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-24 15:11:29,109 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-24 15:11:29,109 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-24 15:11:29,110 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-24 15:11:29,110 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-24 15:11:29,110 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-24 15:11:29,112 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-24 15:11:29,112 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 2.0ms
2025-07-24 15:11:29,112 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-24 15:11:29,113 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-24 15:11:29,113 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-24 15:11:29,113 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-24 15:11:29,113 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 15:11:29,114 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 15:11:29,114 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 15:11:29,114 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 15:11:29,115 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-24 15:11:29,115 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-24 15:11:29,115 - INFO - 收集原始Bin值: 17個設備
2025-07-24 15:11:29,124 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-24 15:11:29,124 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-24 15:11:29,125 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-24 15:11:29,125 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-24 15:11:29,125 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 12.0ms
2025-07-24 15:11:29,125 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-24 15:11:29,125 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 15:11:29,126 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 15:11:29,126 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 15:11:29,126 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 15:11:29,126 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 15:11:29,127 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 15:11:29,169 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-24 15:11:29,170 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-24 15:11:29,170 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 45.0ms
2025-07-24 15:11:29,171 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 63.0ms
2025-07-24 15:11:29,171 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-24 15:11:29,172 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-24 15:11:29,172 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-24 15:11:29,173 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 15:11:29,173 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 15:11:29,174 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 15:11:29,174 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 15:11:29,174 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-24 15:11:29,175 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 15:11:29,175 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 15:11:29,175 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 15:11:29,176 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 15:11:29,176 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 15:11:29,176 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 15:11:29,177 - INFO - 🚀 步驟6-7：開始數據分析和染色處理...
2025-07-24 15:11:29,177 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-24 15:11:29,177 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-24 15:11:29,178 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-24 15:11:29,178 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-24 15:11:29,178 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: 1.0ms
2025-07-24 15:11:29,178 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-24 15:11:29,179 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-24 15:11:29,180 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-24 15:11:29,180 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 2.0ms
2025-07-24 15:11:29,180 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-24 15:11:29,181 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-24 15:11:29,181 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: 1.0ms
2025-07-24 15:11:29,181 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-24 15:11:29,182 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 15:11:29,182 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 15:11:29,182 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 15:11:29,183 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 15:11:29,183 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-24 15:11:29,183 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-24 15:11:29,183 - INFO - 收集原始Bin值: 17個設備
2025-07-24 15:11:29,192 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-24 15:11:29,193 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-24 15:11:29,194 - INFO - VBA 457-469行：填充基本統計 Total=17, Pass=5, Fail=12, Yield=29.41%
2025-07-24 15:11:29,195 - INFO - 添加原始檔案超連結: C:/Users/<USER>/Desktop/test/GMT_G2304.csv
2025-07-24 15:11:29,196 - INFO - 計算實際第4列(Definition列)項目數量: 0
2025-07-24 15:11:29,196 - INFO - Site信息檢查: total_site_no=4, good_site_n=True, site_data數量=16
2025-07-24 15:11:29,196 - INFO - 創建Site統計完成: 4個Site
2025-07-24 15:11:29,197 - INFO -   Site 1: 4個設備
2025-07-24 15:11:29,197 - INFO -   Site 2: 4個設備
2025-07-24 15:11:29,198 - INFO -   Site 3: 4個設備
2025-07-24 15:11:29,198 - INFO -   Site 4: 4個設備
2025-07-24 15:11:29,198 - INFO - 開始填充Site統計: 4個Site, 實際項目數量: 0
2025-07-24 15:11:29,199 - INFO - 填充Site 1統計完成: 4個設備
2025-07-24 15:11:29,199 - INFO - 填充Site 2統計完成: 4個設備
2025-07-24 15:11:29,199 - INFO - 填充Site 3統計完成: 4個設備
2025-07-24 15:11:29,200 - INFO - 填充Site 4統計完成: 4個設備
2025-07-24 15:11:29,200 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 0
2025-07-24 15:11:29,201 - INFO - VBA 362-420行（修正版）：填充了2個測試項目的Bin數據，無重複
2025-07-24 15:11:29,202 - INFO - 設置 AutoFilter 範圍: A6:M8
2025-07-24 15:11:29,202 - INFO - 按 B 欄由大到小排序了 2 行資料
2025-07-24 15:11:29,203 - INFO - 自動調整D列寬度: 14.0 (基於最大內容長度: 10)
2025-07-24 15:11:29,203 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-24 15:11:29,204 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-24 15:11:29,204 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 23.0ms
2025-07-24 15:11:29,205 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-24 15:11:29,205 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 15:11:29,205 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 15:11:29,205 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 15:11:29,205 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 15:11:29,205 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 15:11:29,206 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 15:11:29,267 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-24 15:11:29,267 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-24 15:11:29,268 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 64.0ms
2025-07-24 15:11:29,268 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 91.0ms
2025-07-24 15:11:29,269 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-24 15:11:29,269 - INFO - === 統一處理管道完成 ===
2025-07-24 15:11:32,591 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-24 15:11:33,043 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\GMT_G2304_clean_converted.xlsx，耗時452.0ms
2025-07-24 15:11:33,044 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\GMT_G2304_clean_converted.xlsx
