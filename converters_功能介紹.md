# converters.py 功能介紹文檔

## 概述

`converters.py` 是CSV轉換器的核心模組，包含了完整轉換器和Data11轉換器的實現。該模組負責協調各種處理器，將CTA8280測試儀產生的CSV/SPD檔案轉換為所需的格式。

## 模組結構

```
converters.py
├── BaseConverter (基礎轉換器類別)
├── FullConverter (完整轉換器)
├── Data11Converter (Data11轉換器)
└── 工廠函數 (get_full_converter, get_data11_converter)
```

---

## 🔧 類別和方法詳細說明

### 1. BaseConverter (基礎轉換器類別)

**功能**：所有轉換器的基礎類別，提供共用的功能和屬性。

#### `__init__(self)`
- **功能**：初始化基礎轉換器
- **作用**：
  - 初始化檔案處理器 (`file_handler`)
  - 初始化Excel處理器 (`excel_handler`)
  - 設置測試儀類型為未知 (`TesterType.UNKNOWN`)

#### `detect_tester_type(self, df, file_path=None)`
- **功能**：檢測測試儀類型
- **參數**：
  - `df`: pandas DataFrame - 要檢測的數據
  - `file_path`: str (可選) - 檔案路徑
- **返回值**：bool - 是否檢測到支持的測試儀格式
- **邏輯**：
  - 遍歷DataFrame第一列，尋找 `[Data]` 標記
  - 如果找到，設置測試儀類型為 `CTA8280`
  - 記錄檢測結果到日誌

---

### 2. FullConverter (完整轉換器)

**功能**：產生完整Excel格式的轉換器，包含sum、Data11、QAData工作表。

#### `__init__(self)`
- **功能**：初始化完整轉換器
- **作用**：
  - 繼承基礎轉換器功能
  - 初始化CTA8280格式處理器
  - 初始化空項目名稱填充處理器
  - 初始化Device2BinControl處理器

#### `convert(self, input_file, create_summary=False)`
- **功能**：執行完整轉換的主要方法
- **參數**：
  - `input_file`: str - 輸入檔案路徑
  - `create_summary`: bool - 是否創建Summary工作表
- **返回值**：tuple (bool, str) - (成功狀態, 輸出檔案路徑)
- **處理流程**：
  1. **檔案類型判斷**：
     - SPD檔案 → 調用 `_convert_spd_directly()`
     - CSV檔案 → 標準處理流程
  2. **標準CSV處理**：
     - 讀取CSV檔案
     - 檢測測試儀類型
     - 尋找數據標記 (`[Data]`, `[QAData]`)
     - 分割數據為sum、data11、qa三部分
     - 創建Excel工作簿
  3. **格式處理**：
     - 應用CTA8280格式處理
     - 應用FillEmptyItemName處理
     - 應用Device2BinControl處理
  4. **輸出**：
     - 應用字體格式
     - 保存Excel檔案

#### `_convert_spd_directly(self, input_file, create_summary=False)`
- **功能**：直接將SPD檔案轉換為Excel，跳過CSV步驟以保留顏色
- **參數**：
  - `input_file`: str - SPD檔案路徑
  - `create_summary`: bool - 是否創建Summary工作表
- **返回值**：tuple (bool, str) - (成功狀態, 輸出檔案路徑)
- **特殊處理**：
  - 直接讀取SPD檔案為DataFrame
  - 創建只包含Data11的工作簿
  - 在9A位置寫入"TMT"標記
  - 使用快速模式處理（跳過設備行清理）
  - 保留SPD檔案的顏色格式

---

### 3. Data11Converter (Data11轉換器)

**功能**：產生CSV格式的Data11轉換器，專注於Data11部分的精確處理。

#### `__init__(self)`
- **功能**：初始化Data11轉換器
- **作用**：
  - 繼承基礎轉換器功能
  - 初始化各種處理器（與FullConverter相同）

#### `convert(self, input_file, create_summary=False)`
- **功能**：執行Data11轉換的主要方法
- **參數**：
  - `input_file`: str - 輸入檔案路徑
  - `create_summary`: bool - 是否創建Summary工作表
- **返回值**：str - 輸出檔案路徑
- **處理流程**：
  1. **檔案預處理**：
     - SPD檔案 → 先轉換為CSV格式
     - CSV檔案 → 直接處理
  2. **分支處理**：
     - **SPD轉換的CSV**：
       - 已是Data11格式，直接處理
       - 在9A位置寫入"TMT"
       - 使用快速模式和跳過設備行清理
       - 輸出格式設為"csv"（不執行染色）
     - **標準CSV**：
       - 檢測測試儀類型
       - 尋找數據標記
       - 分割數據
       - 應用各種格式處理
  3. **輸出**：
     - 將工作表轉換為CSV格式
     - 清理臨時檔案

---

## 🏭 工廠函數

### `get_full_converter()`
- **功能**：獲取完整轉換器實例
- **返回值**：FullConverter實例
- **用途**：提供統一的實例創建接口

### `get_data11_converter()`
- **功能**：獲取Data11轉換器實例
- **返回值**：Data11Converter實例
- **用途**：提供統一的實例創建接口

---

## 🔄 處理流程圖

### 完整轉換流程
```
輸入檔案
    ↓
檔案類型判斷
    ├── SPD檔案 → _convert_spd_directly()
    └── CSV檔案 → 標準處理流程
                    ↓
                檢測測試儀類型
                    ↓
                分割數據 (sum/data11/qa)
                    ↓
                創建Excel工作簿
                    ↓
                應用格式處理
                    ↓
                輸出Excel檔案
```

### Data11轉換流程
```
輸入檔案
    ↓
檔案預處理
    ├── SPD檔案 → 轉換為CSV
    └── CSV檔案 → 直接處理
                    ↓
                分支處理
                    ├── SPD轉換的CSV → 直接處理
                    └── 標準CSV → 完整處理流程
                                    ↓
                                輸出CSV檔案
```

---

## 📊 支援的檔案格式

### 輸入格式
- **CSV檔案**：CTA8280測試儀產生的原始CSV檔案
- **SPD檔案**：已處理的CTA8280格式檔案

### 輸出格式
- **完整轉換**：
  - CSV → `原檔名_clean_converted.xlsx`
  - SPD → `原檔名_spd_converted.xlsx`
- **Data11轉換**：
  - CSV → `原檔名_exact_data11.csv`
  - SPD → `原檔名_spd_data11.csv`

---

## ⚙️ 特殊功能

### SPD檔案特殊處理
1. **TMT標記**：自動在9A位置寫入"TMT"
2. **顏色保留**：直接轉換避免顏色丟失
3. **快速模式**：跳過設備行清理以提升性能
4. **數值清理**：使用特殊的數值格式清理

### Summary統計分析
- 可選的Summary工作表生成
- 包含Bin統計、良率分析
- 支援動態Site數量處理

### 錯誤處理
- 完整的異常捕獲和日誌記錄
- 臨時檔案自動清理
- 詳細的處理狀態回報

---

## 🔗 依賴關係

### 內部依賴
- `utils.file_utils` - 檔案處理工具
- `utils.excel_utils` - Excel處理工具
- `core.processors` - 各種數據處理器
- `config.settings` - 配置設定

### 外部依賴
- `pandas` - 數據處理
- `openpyxl` - Excel檔案操作

---

## 💡 使用建議

1. **檔案類型選擇**：
   - 需要完整Excel格式 → 使用 `FullConverter`
   - 只需要Data11部分 → 使用 `Data11Converter`

2. **Summary功能**：
   - 需要詳細統計分析時啟用 `create_summary=True`
   - 會增加處理時間但提供豐富的分析數據

3. **SPD檔案處理**：
   - SPD檔案會自動識別並使用最佳處理方式
   - 保留原有的格式和顏色信息

4. **錯誤處理**：
   - 檢查返回值確認處理狀態
   - 查看日誌檔案獲取詳細錯誤信息
