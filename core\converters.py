#!/usr/bin/env python3
"""
轉換器核心模組
包含完整轉換器和Data11轉換器
"""

import pandas as pd
from utils.file_utils import get_file_handler
from utils.excel_utils import get_excel_handler
from core.processors import get_cta8280_processor, get_fill_empty_processor, get_device2bin_processor
from config.settings import (
    TesterType, DATA_MARKERS, OUTPUT_SUFFIX_FULL, OUTPUT_SUFFIX_EXACT,
    OUTPUT_SUFFIX_SPD_FULL, OUTPUT_SUFFIX_SPD_DATA11
)
import openpyxl
import os

class BaseConverter:
    """基礎轉換器類別"""
    
    def __init__(self):
        self.file_handler = get_file_handler()
        self.excel_handler = get_excel_handler()
        self.my_tester_type = TesterType.UNKNOWN
    
    def detect_tester_type(self, df, file_path=None):
        """檢測測試儀類型"""
        # 檢查是否有[Data]標記（現在SPD檔案也會轉換為CSV格式）
        for i, val in enumerate(df.iloc[:, 0]):
            val_str = str(val) if pd.notna(val) else ""
            if val_str == DATA_MARKERS['DATA']:
                self.my_tester_type = TesterType.CTA8280
                self.file_handler.log_message("檢測到CTA8280測試儀格式（通過[Data]標記）")
                return True

        self.file_handler.log_message("未檢測到支持的測試儀格式")
        return False

class FullConverter(BaseConverter):
    """完整轉換器 - 產生Excel格式"""
    
    def __init__(self):
        super().__init__()
        self.cta8280_processor = get_cta8280_processor()
        self.fill_empty_processor = get_fill_empty_processor()
        self.device2bin_processor = get_device2bin_processor()
    
    def convert(self, input_file, create_summary=False, output_dir=None):
        """執行完整轉換

        Args:
            input_file: 輸入檔案路徑
            create_summary: 是否創建Summary工作表
            output_dir: 輸出目錄路徑（可選）
        """
        self.file_handler.log_message("開始完整轉換...")

        # 處理SPD檔案：直接轉換為Excel（跳過CSV以保留顏色）
        if self.file_handler.is_spd_file(input_file):
            self.file_handler.log_message("檢測到SPD檔案，直接轉換為Excel...")
            return self._convert_spd_directly(input_file, create_summary, output_dir)

        # 處理標準CSV檔案
        temp_csv_file = None  # 初始化變數
        working_file = input_file

        try:
            # 讀取檔案（現在都是CSV格式）
            df = self.file_handler.read_csv_file(working_file)
            if df is None:
                return False, None

            # 標準CSV處理流程
            if not self.detect_tester_type(df, working_file):
                return False, None

            # 按照標準CSV處理流程
            data_row, qa_data_row = self.excel_handler.find_data_markers(df)
            if data_row is None:
                self.file_handler.log_message("未找到 [Data] 標記")
                return False, None

            # 分割數據
            sum_data = df.iloc[:data_row]
            data_end = qa_data_row if qa_data_row is not None else len(df)
            data11_data = df.iloc[data_row:data_end]
            qa_data = df.iloc[qa_data_row+1:] if qa_data_row is not None else None

            # 創建工作簿
            wb, ws_sum, ws_data11 = self.excel_handler.create_workbook_with_sheets(
                sum_data, data11_data, qa_data)

            # 應用完整的CTA8280格式處理
            if self.my_tester_type == TesterType.CTA8280:
                self.file_handler.log_message("應用CTA8280格式...")
                self.cta8280_processor.apply_format(ws_data11, ws_sum)

                # 應用FillEmptyItemName處理
                self.file_handler.log_message("應用FillEmptyItemName處理...")
                self.fill_empty_processor.apply_processing(ws_data11)

                # 應用Device2BinControl處理
                self.file_handler.log_message("應用Device2BinControl處理...")
                self.device2bin_processor.apply_processing(ws_data11, create_summary=create_summary, original_filename=input_file)

            # 應用字體格式
            self.excel_handler.apply_font_formatting(wb)

            # 優化9: 使用優化的保存方法
            if self.file_handler.is_spd_file(input_file):
                output_file = self.file_handler.generate_output_filename(input_file, OUTPUT_SUFFIX_SPD_FULL, output_dir)
            else:
                output_file = self.file_handler.generate_output_filename(input_file, OUTPUT_SUFFIX_FULL, output_dir)

            # 使用優化的保存方法
            save_success = self.excel_handler.save_workbook_optimized(wb, output_file)
            if not save_success:
                raise Exception("Excel檔案保存失敗")

            self.file_handler.log_message(f"完整轉換完成！輸出文件: {output_file}")
            return True, output_file

        finally:
            # 清理臨時CSV檔案
            if temp_csv_file:
                self.file_handler.cleanup_temp_file(temp_csv_file)

    def _convert_spd_directly(self, input_file, create_summary=False, output_dir=None):
        """直接將SPD檔案轉換為Excel，跳過CSV步驟以保留顏色"""
        try:
            # 直接讀取SPD檔案為DataFrame
            df = self.file_handler.read_csv_file(input_file)
            if df is None:
                return False, None

            self.file_handler.log_message("SPD檔案直接轉換，已是Data11格式")

            # 創建只包含Data11的工作簿（不需要sum工作表）
            from openpyxl import Workbook
            wb = Workbook()
            ws_data11 = wb.active
            ws_data11.title = "Data11"

            # 將DataFrame寫入工作表（SPD文件使用數值清理）
            self.excel_handler.write_dataframe_to_worksheet(df, ws_data11, clean_numeric_format=True)

            # SPD檔案特殊處理：在9A位置寫入"TMT"
            self.file_handler.log_message("SPD檔案：在9A位置寫入TMT...")
            ws_data11.cell(9, 1).value = "TMT"

            # 只應用FillEmptyItemName處理（SPD檔案使用快速模式和跳過設備行清理）
            self.file_handler.log_message("應用FillEmptyItemName處理...")
            self.fill_empty_processor.apply_processing(ws_data11, skip_device_cleanup=True, fast_mode=True)

            # 應用Device2BinControl處理（包含染色功能）
            self.file_handler.log_message("應用Device2BinControl處理...")
            self.device2bin_processor.apply_processing(ws_data11, create_summary=create_summary, original_filename=input_file)

            # 應用字體格式
            self.excel_handler.apply_font_formatting(wb)

            # 生成輸出檔案名
            output_file = self.file_handler.generate_output_filename(input_file, "_spd_converted.xlsx", output_dir)

            # 優化10: 使用優化的保存方法
            save_success = self.excel_handler.save_workbook_optimized(wb, output_file)
            if not save_success:
                raise Exception("SPD Excel檔案保存失敗")
            self.file_handler.log_message(f"SPD直接轉換完成！輸出文件: {output_file}")

            return True, output_file

        except Exception as e:
            self.file_handler.log_message(f"SPD直接轉換過程中出錯: {e}")
            return False, None

class Data11Converter(BaseConverter):
    """Data11轉換器 - 產生CSV格式"""
    
    def __init__(self):
        super().__init__()
        self.cta8280_processor = get_cta8280_processor()
        self.fill_empty_processor = get_fill_empty_processor()
        self.device2bin_processor = get_device2bin_processor()
    
    def convert(self, input_file, create_summary=False):
        """執行Data11轉換"""
        self.file_handler.log_message("開始精確Data11轉換...")

        # 處理SPD檔案：先轉換為CSV
        temp_csv_file = None
        working_file = input_file

        if self.file_handler.is_spd_file(input_file):
            self.file_handler.log_message("檢測到SPD檔案，先轉換為CSV格式...")
            temp_csv_file = self.file_handler.convert_spd_to_csv(input_file)
            if temp_csv_file is None:
                return None
            working_file = temp_csv_file

        try:
            # 讀取檔案（現在都是CSV格式）
            df = self.file_handler.read_csv_file(working_file)
            if df is None:
                return None

            # 判斷是否為SPD轉換的CSV（已經是CTA8280格式）
            is_spd_converted = self.file_handler.is_spd_file(input_file)

            if is_spd_converted:
                # SPD轉換的CSV已經是Data11格式，直接處理
                self.file_handler.log_message("處理SPD轉換的CSV，已是Data11格式")

                # 創建只包含Data11的工作簿
                from openpyxl import Workbook
                wb = Workbook()
                ws_data11 = wb.active
                ws_data11.title = "Data11"

                # 將DataFrame寫入工作表（SPD文件使用數值清理）
                self.excel_handler.write_dataframe_to_worksheet(df, ws_data11, clean_numeric_format=True)

                # SPD檔案特殊處理：在9A位置寫入"TMT"
                self.file_handler.log_message("SPD檔案：在9A位置寫入TMT...")
                ws_data11.cell(9, 1).value = "TMT"

                # 只應用FillEmptyItemName處理（SPD檔案使用快速模式和跳過設備行清理）
                self.file_handler.log_message("應用FillEmptyItemName處理...")
                self.fill_empty_processor.apply_processing(ws_data11, skip_device_cleanup=True, fast_mode=True)

                # 應用Device2BinControl處理（CSV格式，不執行染色）
                self.file_handler.log_message("應用Device2BinControl處理...")
                self.device2bin_processor.apply_processing(ws_data11, create_summary=create_summary, output_format="csv")

            else:
                # 標準CSV處理流程
                if not self.detect_tester_type(df, working_file):
                    return None

                # 按照標準CSV處理流程
                data_row, qa_data_row = self.excel_handler.find_data_markers(df)
                if data_row is None:
                    self.file_handler.log_message("未找到 [Data] 標記")
                    return None

                # 分割數據
                sum_data = df.iloc[:data_row]
                data_end = qa_data_row if qa_data_row is not None else len(df)
                data11_data = df.iloc[data_row:data_end]

                # 創建工作簿（只需要sum和Data11）
                wb, ws_sum, ws_data11 = self.excel_handler.create_workbook_with_sheets(
                    sum_data, data11_data)

                # 應用CTA8280格式處理
                self.cta8280_processor.apply_format(ws_data11, ws_sum)

                # 應用FillEmptyItemName處理
                self.fill_empty_processor.apply_processing(ws_data11)

                # 應用Device2BinControl處理（CSV格式，不執行染色）
                self.file_handler.log_message("應用Device2BinControl處理...")
                self.device2bin_processor.apply_processing(ws_data11, create_summary=create_summary, output_format="csv")

            # 直接轉換為CSV
            if self.file_handler.is_spd_file(input_file):
                output_file = self.file_handler.generate_output_filename(input_file, OUTPUT_SUFFIX_SPD_DATA11)
            else:
                output_file = self.file_handler.generate_output_filename(input_file, OUTPUT_SUFFIX_EXACT)
            self.excel_handler.worksheet_to_csv(ws_data11, output_file)

            self.file_handler.log_message(f"精確轉換完成！輸出文件: {output_file}")
            return output_file

        finally:
            # 清理臨時CSV檔案
            if temp_csv_file:
                self.file_handler.cleanup_temp_file(temp_csv_file)


def get_full_converter():
    """獲取完整轉換器實例"""
    return FullConverter()

def get_data11_converter():
    """獲取Data11轉換器實例"""
    return Data11Converter()

class SummaryCSVConverter(BaseConverter):
    """Summary CSV轉換器 - 專門產生Summary工作表並另存為CSV"""

    def __init__(self):
        super().__init__()
        self.cta8280_processor = get_cta8280_processor()
        self.fill_empty_processor = get_fill_empty_processor()
        self.device2bin_processor = get_device2bin_processor()

    def convert(self, input_file, original_zip_file=None, output_dir=None):
        """執行Summary CSV轉換

        Args:
            input_file: 輸入檔案路徑（可能是解壓後的檔案）
            original_zip_file: 原始ZIP檔案路徑（如果是從ZIP解壓的）
            output_dir: 輸出目錄路徑（可選）
        """
        self.file_handler.log_message("開始Summary CSV轉換...")

        try:
            # 步驟1: 讀取CSV文件
            df = self.file_handler.read_csv_file(input_file)
            if df is None:
                return False, None

            # 步驟2: 檢測測試儀類型（SPD文件特殊處理）
            if self.file_handler.is_spd_file(input_file):
                self.file_handler.log_message("檢測到SPD文件，使用SPD專用處理邏輯")
                # SPD文件直接設置為支持的類型，跳過標準檢測
                self.my_tester_type = TesterType.CTA8280  # SPD文件通常是CTA8280格式
            else:
                # 標準文件使用標準檢測
                if not self.detect_tester_type(df, input_file):
                    self.file_handler.log_message("不支持的文件格式")
                    return False, None

            # 步驟3: 創建臨時Excel文件進行完整處理
            temp_excel_file = self._create_temp_excel_with_summary(df, input_file)
            if not temp_excel_file:
                return False, None

            # 步驟4: 從Excel文件中提取Summary工作表並轉為CSV
            # 如果有原始ZIP檔案，使用ZIP檔案路徑作為連結
            link_file = original_zip_file if original_zip_file else input_file
            summary_csv_file = self._extract_summary_to_csv(temp_excel_file, link_file, output_dir)

            # 步驟5: 清理臨時文件
            try:
                if os.path.exists(temp_excel_file):
                    os.remove(temp_excel_file)
            except Exception as e:
                self.file_handler.log_message(f"清理臨時文件時出錯: {e}")

            if summary_csv_file:
                self.file_handler.log_message(f"Summary CSV轉換完成！輸出文件: {summary_csv_file}")
                return True, summary_csv_file
            else:
                return False, None

        except Exception as e:
            self.file_handler.log_message(f"Summary CSV轉換失敗: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")
            return False, None

    def _create_temp_excel_with_summary(self, df, input_file):
        """創建包含Summary的臨時Excel文件"""
        try:
            # 生成臨時文件名（支持SPD和CSV文件）
            if self.file_handler.is_spd_file(input_file):
                temp_file = input_file.replace('.spd', '_temp_summary.xlsx')
            else:
                temp_file = input_file.replace('.csv', '_temp_summary.xlsx')

            self.file_handler.log_message("使用完整轉換邏輯創建臨時文件...")

            # 使用FullConverter來處理，確保所有步驟都正確執行
            from core.converters import FullConverter
            full_converter = FullConverter()

            # 執行完整轉換到臨時文件
            success, full_output = full_converter.convert(input_file, create_summary=True)

            if success and full_output:
                # 將完整轉換的結果複製為臨時文件
                import shutil
                shutil.copy2(full_output, temp_file)

                self.file_handler.log_message(f"使用完整轉換創建臨時Excel文件: {temp_file}")

                # 清理完整轉換的輸出文件（我們只需要臨時文件）
                try:
                    if os.path.exists(full_output):
                        os.remove(full_output)
                        self.file_handler.log_message(f"清理完整轉換輸出文件: {full_output}")
                except Exception as cleanup_e:
                    self.file_handler.log_message(f"清理完整轉換輸出文件時出錯: {cleanup_e}")

                return temp_file
            else:
                self.file_handler.log_message("完整轉換失敗，無法創建臨時文件")
                return None

        except Exception as e:
            self.file_handler.log_message(f"創建臨時Excel文件失敗: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")
            return None

    def _extract_summary_to_csv(self, excel_file, original_input_file, output_dir=None):
        """從Excel文件中提取Summary工作表並轉為CSV"""
        try:
            # 打開Excel文件
            wb = openpyxl.load_workbook(excel_file)

            if 'Summary' not in wb.sheetnames:
                self.file_handler.log_message("Excel文件中沒有找到Summary工作表")
                wb.close()
                return None

            # 獲取Summary工作表
            summary_ws = wb['Summary']
            self.file_handler.log_message(f"Summary工作表大小: {summary_ws.max_row} x {summary_ws.max_column}")

            # 完整CSV結構：按Excel Summary工作表的原始順序輸出
            # 不使用標題行，直接輸出所有數據行

            # 提取所有數據行（第1-8行）
            all_data = []
            max_cols = summary_ws.max_column

            self.file_handler.log_message(f"提取Summary工作表所有數據（第1-{summary_ws.max_row}行）...")
            for row in range(1, summary_ws.max_row + 1):
                row_data = []
                has_data = False

                for col in range(1, max_cols + 1):
                    cell_value = summary_ws.cell(row, col).value
                    if cell_value is not None:
                        row_data.append(str(cell_value))
                        has_data = True
                    else:
                        row_data.append("")

                if has_data:
                    all_data.append(row_data)
                    # 移除詳細的Summary行信息日誌

            # 修正C2位置的原始檔案路徑
            if original_input_file and len(all_data) >= 2:
                # 確保第2行有足夠的列數
                while len(all_data[1]) < 3:
                    all_data[1].append("")

                # 在C2位置（第2行第3列）放置原始檔案的完整路徑
                import os
                full_path = os.path.abspath(original_input_file)
                all_data[1][2] = full_path
                self.file_handler.log_message(f"在Summary CSV的C2位置設置原始檔案路徑: {full_path}")

            # 不使用標題行，直接輸出數據
            # 使用第一行作為假標題，但實際上所有行都是數據
            headers = all_data[0] if all_data else []
            data = all_data[1:] if all_data else []  # 跳過第一行，避免重複



            wb.close()

            # 創建DataFrame
            if data and headers:
                df_summary = pd.DataFrame(data, columns=headers)

                # 生成輸出文件名
                output_file = self._generate_summary_csv_filename(original_input_file, output_dir)

                # 保存為CSV
                df_summary.to_csv(output_file, index=False, encoding='utf-8-sig')

                self.file_handler.log_message(f"Summary工作表已轉換為CSV: {output_file}")
                self.file_handler.log_message(f"Summary數據: {len(data)}行 × {len(headers)}列")
                self.file_handler.log_message(f"標題: {headers}")

                return output_file
            else:
                self.file_handler.log_message(f"Summary工作表中沒有找到有效數據。標題: {headers}, 數據行數: {len(data)}")
                return None

        except Exception as e:
            self.file_handler.log_message(f"提取Summary工作表失敗: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")
            return None

    def _generate_summary_csv_filename(self, input_file, output_dir=None):
        """生成Summary CSV文件名"""
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        filename = f"{base_name}_summary.csv"

        if output_dir:
            return os.path.join(output_dir, filename)
        else:
            # 如果沒有指定輸出目錄，使用輸入檔案的目錄
            input_dir = os.path.dirname(input_file)
            return os.path.join(input_dir, filename)

    def _create_basic_summary_if_needed(self, wb, ws_data11):
        """如果沒有Summary工作表，創建一個基本的Summary"""
        try:
            if 'Summary' not in wb.sheetnames:
                self.file_handler.log_message("創建基本Summary工作表...")

                # 創建Summary工作表
                summary_ws = wb.create_sheet("Summary")

                # 添加基本標題
                summary_ws.cell(6, 1).value = "Bin"
                summary_ws.cell(6, 2).value = "Count"
                summary_ws.cell(6, 3).value = "%"
                summary_ws.cell(6, 4).value = "Definition"
                summary_ws.cell(6, 5).value = "Note"

                # 添加基本數據（如果有Bin數據的話）
                summary_ws.cell(7, 1).value = "1"
                summary_ws.cell(7, 2).value = "0"
                summary_ws.cell(7, 3).value = "0%"
                summary_ws.cell(7, 4).value = "All Pass"
                summary_ws.cell(7, 5).value = ""

                self.file_handler.log_message("基本Summary工作表創建完成")

        except Exception as e:
            self.file_handler.log_message(f"創建基本Summary工作表失敗: {e}")

def get_summary_csv_converter():
    """獲取Summary CSV轉換器實例"""
    return SummaryCSVConverter()
