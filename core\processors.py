#!/usr/bin/env python3
"""
資料處理器模組 - 統一入口
重構後的處理器模組，提供所有處理器類的統一入口
"""

# 從子模組導入所有處理器類
from .processors.base_processor import BaseProcessor
from .processors.cta8280_processor import CTA8280Processor
from .processors.fill_empty_processor import FillEmptyItemNameProcessor
from .processors.device_bin_processor import Device2BinControlProcessor
from .processors.processor_utils import _get_total_device_number_common

# 為了保持向後兼容性，導出所有類
__all__ = [
    'BaseProcessor',
    'CTA8280Processor',
    'FillEmptyItemNameProcessor',
    'Device2BinControlProcessor',
    '_get_total_device_number_common',
    'get_cta8280_processor',
    'get_fill_empty_processor',
    'get_device2bin_processor'
]

# 重構完成！
# 原來的2230行代碼已經分解為：
# - base_processor.py: 基礎處理器類（共用功能）
# - cta8280_processor.py: CTA8280格式處理器
# - fill_empty_processor.py: FillEmptyItemName處理器
# - device_bin_processor.py: Device2BinControl處理器
# - processor_utils.py: 共用工具函數

# 這樣的結構有以下優點：
# 1. 代碼更容易維護和修改
# 2. 每個處理器職責單一，邏輯清晰
# 3. 減少了文件大小，提高了可讀性
# 4. 便於單元測試和調試
# 5. 支援模組化開發

# 注意：工廠函數現在在 core/processors/__init__.py 中定義
