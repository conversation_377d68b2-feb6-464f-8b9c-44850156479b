#!/usr/bin/env python3
"""
基礎處理器模組
包含所有處理器的共用功能
"""

import time
from openpyxl.styles import Font, Color
from utils.file_utils import get_file_handler


class BaseProcessor:
    """基礎處理器類 - 包含所有處理器的共用功能"""

    def __init__(self):
        """初始化基礎處理器"""
        self.file_handler = get_file_handler()

    def _find_column_by_keywords(self, ws, row, keywords, max_col=20):
        """統一的列搜索方法（優化版）

        Args:
            ws: 工作表
            row: 搜索的行號
            keywords: 關鍵字列表
            max_col: 最大搜索列數

        Returns:
            int: 找到的列號，未找到返回None
        """
        # 優化：限制搜索範圍，提高效率
        search_limit = min(ws.max_column + 1, max_col)

        for col in range(1, search_limit):
            cell_value = ws.cell(row, col).value
            if cell_value:
                cell_str = str(cell_value).upper()  # 統一轉大寫比較
                if any(keyword.upper() in cell_str for keyword in keywords):
                    return col
        return None

    def _find_column_by_keywords_fast(self, ws, row, keywords, max_col=20):
        """快速列搜索方法（批量優化版）

        Args:
            ws: 工作表
            row: 搜索的行號
            keywords: 關鍵字列表
            max_col: 最大搜索列數

        Returns:
            int: 找到的列號，未找到返回None
        """
        # 批量讀取單元格值，減少單個訪問
        search_limit = min(ws.max_column + 1, max_col)

        # 批量檢查，每次檢查5個單元格
        batch_size = 5
        for col_start in range(1, search_limit, batch_size):
            col_end = min(col_start + batch_size, search_limit)

            for col in range(col_start, col_end):
                cell_value = ws.cell(row, col).value
                if cell_value:
                    cell_str = str(cell_value).upper()
                    if any(keyword.upper() in cell_str for keyword in keywords):
                        return col
        return None

    def _iterate_device_rows_with_data(self, ws, start_row=13, check_data_cols=None):
        """遍歷有數據的設備行

        Args:
            ws: 工作表
            start_row: 開始行號
            check_data_cols: 檢查數據的列範圍，默認為3-20列

        Yields:
            int: 有數據的行號
        """
        if check_data_cols is None:
            check_data_cols = range(3, min(ws.max_column + 1, 20))

        for row_num in range(start_row, ws.max_row + 1):
            # 檢查是否有實際數據
            has_data = any(
                ws.cell(row_num, col).value is not None and str(ws.cell(row_num, col).value).strip()
                for col in check_data_cols
            )
            if has_data:
                yield row_num

    def _is_numeric(self, value, allow_tilde=False):
        """統一的數字檢查方法（整合版）

        Args:
            value: 要檢查的值
            allow_tilde: 是否允許波浪號和點分隔格式

        Returns:
            bool: 是否為有效的數字格式
        """
        if not value:
            return False

        str_value = str(value).strip()

        # 檢查純數字
        try:
            float(str_value)
            return True
        except (ValueError, TypeError):
            pass

        # 如果允許擴展格式
        if allow_tilde:
            # 檢查是否包含波浪號
            if "~" in str_value:
                return True

            # 檢查是否為點分隔的數字格式
            if "." in str_value:
                parts = str_value.split(".")
                try:
                    for part in parts:
                        if part:
                            float(part)
                    return True
                except ValueError:
                    pass

        return False

    def _is_numeric_or_tilde(self, value):
        """數字或波浪號檢查方法（統一版）

        這是 _is_numeric(allow_tilde=True) 的便捷方法
        """
        return self._is_numeric(value, allow_tilde=True)

    def _iterate_device_rows_with_data(self, ws, start_row=13, max_rows=None):
        """統一的設備行遍歷方法

        Args:
            ws: 工作表
            start_row: 開始行號（預設13）
            max_rows: 最大行數限制

        Yields:
            int: 有數據的設備行號
        """
        if max_rows is None:
            max_rows = ws.max_row
        else:
            max_rows = min(ws.max_row, max_rows)

        for row_num in range(start_row, max_rows + 1):
            # 檢查第一列是否有數據（設備編號）
            if ws.cell(row_num, 1).value is not None:
                yield row_num

    def _get_total_device_number(self, ws, max_rows=None):
        """統一的設備數量計算方法

        Args:
            ws: 工作表
            max_rows: 最大行數限制

        Returns:
            int: 設備總數
        """
        device_count = 0
        for _ in self._iterate_device_rows_with_data(ws, max_rows=max_rows):
            device_count += 1
        return device_count

    def _format_percentage(self, value):
        """統一的百分比格式化方法

        Args:
            value: 數值

        Returns:
            str: 格式化的百分比字符串（用於顯示）
        """
        if value == 0:
            return "0%"
        else:
            return f"{value:.2f}%"

    def _get_percentage_value(self, value):
        """獲取百分比的數值（用於Excel數字格式）

        Args:
            value: 百分比數值（如 25.5 表示 25.5%）

        Returns:
            float: 百分比的小數值（如 0.255）
        """
        return value / 100.0

    def _format_item_number(self, cur_item_n, sub_item_n):
        """統一的測試項目編號格式化方法

        Args:
            cur_item_n: 當前項目編號
            sub_item_n: 子項目編號

        Returns:
            str: 格式化的項目編號
        """
        from config.settings import FILL_EMPTY_SETTINGS
        return FILL_EMPTY_SETTINGS['ITEM_NUMBER_FORMAT'].format(cur_item_n, sub_item_n)

    def _get_column_letter(self, col_num):
        """將列號轉換為列字母

        Args:
            col_num: 列號（1-based）

        Returns:
            str: 列字母（如A, B, C...）
        """
        result = ""
        while col_num > 0:
            col_num -= 1
            result = chr(col_num % 26 + ord('A')) + result
            col_num //= 26
        return result

    def _log_error(self, process_name, error):
        """記錄錯誤日誌"""
        self.file_handler.log_message(f"{process_name}時出錯: {error}")
        import traceback
        self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")

    def _log_timing(self, process_name, start_time, end_time=None):
        """記錄執行時間日誌

        Args:
            process_name: 處理名稱
            start_time: 開始時間
            end_time: 結束時間，如果為None則使用當前時間
        """
        if end_time is None:
            end_time = time.time()
        duration = end_time - start_time
        if duration < 0.001:
            self.file_handler.log_message(f"⏱️ {process_name} 執行時間: <1ms")
        elif duration < 1:
            self.file_handler.log_message(f"⏱️ {process_name} 執行時間: {duration*1000:.1f}ms")
        else:
            self.file_handler.log_message(f"⏱️ {process_name} 執行時間: {duration:.2f}s")
