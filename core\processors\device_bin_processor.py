#!/usr/bin/env python3
"""
Device2BinControl處理器模組 - 基於VBA程式轉換
"""

import time
from openpyxl.styles import Font
from .base_processor import BaseProcessor
# 注意：_get_total_device_number_common 已移到 BaseProcessor 中


class Device2BinControlProcessor(BaseProcessor):
    """Device2BinControl處理器 - 專門負責步驟6-7：數據分析、fail染色、Summary生成"""

    def __init__(self):
        super().__init__()

    def apply_processing(self, ws, create_summary=False, output_format="excel", original_filename=None, data_summary=None):
        """應用Device2BinControl處理 - 步驟6-7：數據分析和染色

        Args:
            ws: 工作表
            create_summary: 是否創建Summary
            output_format: 輸出格式
            original_filename: 原始檔案名
            data_summary: 步驟6傳遞的統一數據摘要
        """
        overall_start = time.time()
        self.file_handler.log_message("🚀 步驟6-7：開始數據分析和染色處理...")

        # 如果沒有傳遞數據摘要，則使用舊的方式（向後兼容）
        if data_summary is None:
            self.file_handler.log_message("⚠️ 未收到步驟6的數據摘要，使用向後兼容模式")
            data_summary = self.collect_unified_data_summary(ws)

        # 儲存原始檔案名供後續使用
        self.original_filename = original_filename

        # 步驟1: 檢查工作表格式
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟1：開始工作表格式驗證...")
        if not self._validate_worksheet_format(ws):
            self.file_handler.log_message("❌ 步驟1：工作表格式不符合Device2BinControl要求")
            return False
        self._log_timing("步驟1-工作表格式驗證", start_time)

        # 步驟2: 獲取原始格式類型（由步驟5設定）
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟2：開始獲取原始格式類型...")
        original_format = self._get_original_format_type(ws)
        self._log_timing("步驟2-獲取原始格式類型", start_time)
        self.file_handler.log_message(f"✅ 步驟2：原始格式類型: {original_format}")

        # 步驟3: 設置Excel格式
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟3：開始Excel格式設置...")
        self._setup_excel_formatting(ws)
        self._log_timing("步驟3-Excel格式設置", start_time)

        # 步驟4: 使用統一數據摘要（無需重複計算設備數據）
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟4：使用統一數據摘要...")
        self.file_handler.log_message(f"✅ 數據摘要: 項目{data_summary['item_count']}個, 設備{data_summary['device_count']}個, Site{data_summary['site_info']['total_site_no']}個")
        self._log_timing("步驟4-使用統一數據摘要", start_time)

        # 7.1 VBA核心分析（統一數據版）
        start_time = time.time()
        self.file_handler.log_message("🔄 7.1 開始VBA核心分析（統一數據版）...")
        self.file_handler.log_message("  - 7.1.1 收集原始Bin值")
        self.file_handler.log_message("  - 7.1.2 執行設備分析（測試項目失敗檢測）")
        self.file_handler.log_message("  - 7.1.3 計算Bin統計")
        self.file_handler.log_message("  - 7.1.4 應用染色邏輯（Excel格式）")
        self._execute_vba_core_analysis_unified(ws, data_summary, create_summary, output_format, original_filename)
        self._log_timing("7.1-VBA核心分析", start_time)

        # 7.2 創建增強Summary工作表（僅Excel格式，使用統一數據）
        if output_format.lower() == "excel":
            start_time = time.time()
            self.file_handler.log_message("🔄 7.2 開始創建增強Summary工作表...")
            self.file_handler.log_message("  - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）")
            self.file_handler.log_message("  - 7.2.2 添加原始檔案超連結（C1）")
            self.file_handler.log_message("  - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）")
            self.file_handler.log_message("  - 7.2.4 填充F列開始的Site統計（數量和百分比）")
            self.file_handler.log_message("  - 7.2.5 填充測試項目Bin數據")
            self.file_handler.log_message("  - 7.2.6 設置AutoFilter和排序")
            self._set_font_colors_unified(ws, data_summary)
            self._log_timing("7.2-創建增強Summary工作表", start_time)
        else:
            self.file_handler.log_message("⏭️ 步驟6：跳過字體顏色設置（CSV格式）")

        self._log_timing("步驟7 Summary工作表生成總時間", overall_start)
        self.file_handler.log_message("✅ 步驟7：Summary工作表生成完成")
        return True

    def _execute_vba_core_analysis_unified(self, ws, data_summary, create_summary, output_format, original_filename):
        """執行VBA核心分析（使用統一數據，完整版本 - 基於原始VBA 159-361行）"""
        try:
            self.file_handler.log_message("開始VBA核心分析（統一數據完整版）...")

            # 直接使用統一數據摘要，無需重複計算
            device_count = data_summary['device_count']
            item_count = data_summary['item_count']
            site_info = data_summary['site_info']

            if device_count == 0:
                self.file_handler.log_message("沒有設備數據，跳過VBA核心分析")
                return

            self.file_handler.log_message(f"使用統一數據: 設備{device_count}個, 項目{item_count}個")

            # 子步驟5.1: 收集原始Bin值（使用統一數據）
            original_bins = self._collect_original_bins_unified(data_summary)

            # 子步驟5.2: 收集限制值（使用統一數據）
            max_limits = data_summary['max_limits']
            min_limits = data_summary['min_limits']

            # 子步驟5.3: 執行設備分析（使用統一數據）
            analysis_result = self._perform_device_analysis_unified(
                ws, data_summary, original_bins, max_limits, min_limits, output_format
            )

            # 子步驟5.4: 計算Bin統計
            bin_statistics = self._calculate_bin_statistics_unified(analysis_result, device_count)

            # 子步驟5.5: 創建Summary工作表（僅Excel格式且需要Summary時）
            if create_summary and output_format.lower() == "excel":
                self._create_enhanced_summary_unified(
                    ws, data_summary, analysis_result, bin_statistics, original_filename
                )
            elif create_summary:
                self.file_handler.log_message("⏭️ 跳過Summary工作表創建（CSV格式）")
            else:
                self.file_handler.log_message("⏭️ 跳過Summary工作表創建（未要求創建）")

            self.file_handler.log_message("VBA核心分析完成（統一數據完整版）")

        except Exception as e:
            self.file_handler.log_message(f"VBA核心分析時出錯: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")

    def _analyze_device_bins_unified(self, ws, data_summary):
        """分析設備Bin信息（使用統一數據）"""
        my_bin_array = {}

        # 直接使用統一數據中的設備Bin信息
        for device_row, bin_value in data_summary['device_bins'].items():
            try:
                bin_num = int(float(str(bin_value))) if bin_value else 1
                my_bin_array[device_row] = bin_num
            except (ValueError, TypeError):
                my_bin_array[device_row] = 1

        # 為沒有Bin值的設備設置預設值
        for device_row in data_summary['device_rows']:
            if device_row not in my_bin_array:
                my_bin_array[device_row] = 1

        self.file_handler.log_message(f"分析設備Bin完成: {len(my_bin_array)}個設備")
        return my_bin_array

    def _collect_original_bins_unified(self, data_summary):
        """收集原始Bin值（使用統一數據）"""
        original_bins = {}
        for device_row in data_summary['device_rows']:
            bin_value = data_summary['device_bins'].get(device_row, 1)
            try:
                original_bins[device_row] = int(float(str(bin_value))) if bin_value else 1
            except (ValueError, TypeError):
                original_bins[device_row] = 1

        self.file_handler.log_message(f"收集原始Bin值: {len(original_bins)}個設備")
        return original_bins

    def _perform_device_analysis_unified(self, ws, data_summary, original_bins, max_limits, min_limits, output_format):
        """執行設備分析（使用統一數據，基於VBA 187-335行）"""
        device_count = data_summary['device_count']
        item_count = data_summary['item_count']
        device_rows = data_summary['device_rows']

        device_bin_results = {}
        failed_items = {}

        # VBA參數設置
        max_pass_bin_n = 1
        my_bin1 = True  # 通常為True
        my_tester_type = 1  # 預設值

        # 分析每個設備（VBA 187-335行主循環）
        for device_row in device_rows:
            device_failed_items = []

            # VBA邏輯：獲取設備的原始Bin值
            original_bin_value = original_bins[device_row]

            # VBA關鍵條件（行201）：If myBinN(myLoopDevice) > myMaxPassBinN Or myBin1 Then
            should_analyze = (original_bin_value > max_pass_bin_n) or my_bin1

            if should_analyze:
                # 執行完整的測試項目分析（VBA 202-306行）
                device_failed_items = self._analyze_device_with_full_logic_unified(
                    device_row, data_summary, ws, max_limits, min_limits, my_tester_type
                )

                # 根據失敗項目計算最終Bin值
                if device_failed_items:
                    # 使用第一個失敗項目的Bin值
                    device_bin_results[device_row] = device_failed_items[0]['bin']
                else:
                    # 沒有失敗項目，設為Pass Bin (1)
                    device_bin_results[device_row] = 1
            else:
                # VBA行308：直接使用原始Bin值
                device_bin_results[device_row] = original_bin_value
                device_failed_items = []  # 不分析則沒有失敗項目

            # 記錄失敗項目
            if device_failed_items:
                failed_items[device_row] = device_failed_items

        # 只有Excel格式才應用染色邏輯（VBA 311-333行）
        if output_format.lower() == "excel":
            self._apply_coloring_logic_unified(ws, failed_items, data_summary)
        else:
            self.file_handler.log_message("CSV格式輸出，跳過染色邏輯")

        # 返回分析結果
        return {
            'device_bin_results': device_bin_results,
            'failed_items': failed_items,
            'device_count': device_count,
            'item_count': item_count,
            'site_info': data_summary['site_info']
        }

    def _calculate_bin_statistics_unified(self, analysis_result, device_count):
        """計算Bin統計（使用統一數據）"""
        device_bin_results = analysis_result['device_bin_results']

        # 計算Pass/Fail統計
        pass_count = sum(1 for bin_val in device_bin_results.values() if bin_val == 1)
        fail_count = device_count - pass_count

        bin_statistics = {
            'pass_devices': pass_count,
            'fail_devices': fail_count,
            'total_devices': device_count
        }

        self.file_handler.log_message(f"Bin統計: Pass={pass_count}, Fail={fail_count}, Total={device_count}")
        return bin_statistics

    def _create_enhanced_summary_unified(self, ws, data_summary, analysis_result, bin_statistics, original_filename):
        """創建增強的Summary工作表（使用統一數據，完整版本）"""
        try:
            # 創建Summary工作表
            wb = ws.parent
            if "Summary" in wb.sheetnames:
                summary_ws = wb["Summary"]
                wb.remove(summary_ws)

            summary_ws = wb.create_sheet("Summary")

            # 1. 填充A1~C4的基本統計信息（必須在其他內容之前）
            self._fill_summary_basic_info(summary_ws, bin_statistics, data_summary['device_count'])

            # 2. 添加原始檔案連結到C1（在基本統計信息之後）
            self._add_original_file_link_unified(summary_ws, original_filename)

            # 3. 填充Bin 1 (All Pass)
            my_bin_array = analysis_result['device_bin_results']
            bin1_count = sum(1 for bin_val in my_bin_array.values() if bin_val == 1)

            # 4. 設置標題行和Site統計（使用統一數據，包含F列開始的Site統計）
            self._setup_summary_headers_and_site_stats_vba_421_448(
                summary_ws, ws, my_bin_array,
                data_summary['device_count'],
                analysis_result,
                data_summary['site_info']
            )

            # 5. 填充測試項目Bin數據
            self._fill_test_item_bins(
                summary_ws, ws, my_bin_array,
                data_summary['device_count'],
                analysis_result
            )

            # 6. 應用VBA風格的格式化（包含篩選和排序）
            self._apply_vba_style_formatting(summary_ws)

            self.file_handler.log_message("增強Summary工作表創建完成（統一數據版）")

        except Exception as e:
            self.file_handler.log_message(f"創建增強Summary工作表時出錯: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")

    def _create_summary_unified(self, ws, data_summary, my_bin_array, original_filename):
        """創建Summary（使用統一數據，完整版本）"""
        try:
            # 創建Summary工作表
            wb = ws.parent
            summary_ws = wb.create_sheet("Summary")

            # 1. 設置原始檔案連結（第1行）
            if original_filename:
                summary_ws.cell(1, 1).value = "原始檔案:"
                summary_ws.cell(1, 2).value = original_filename
                self.file_handler.log_message(f"設置原始檔案連結: {original_filename}")

            # 2. 設置Summary標題和Site統計（使用統一數據）
            self._setup_summary_headers_and_site_stats_vba_421_448(
                summary_ws, ws, my_bin_array,
                data_summary['device_count'],
                None,  # analysis_result
                data_summary['site_info']
            )

            # 3. 填充測試項目Bin數據（使用統一數據）
            self._fill_test_item_bins(
                summary_ws, ws, my_bin_array,
                data_summary['device_count'],
                None  # analysis_result
            )

            # 4. 添加各Site的fail統計
            self._add_site_fail_statistics_unified(summary_ws, data_summary, my_bin_array)

            # 5. 設置AutoFilter和排序
            self._setup_summary_autofilter_and_sort(summary_ws)

            self.file_handler.log_message("Summary創建完成（統一數據完整版）")

        except Exception as e:
            self.file_handler.log_message(f"創建Summary時出錯: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")

    def _set_font_colors_unified(self, ws, data_summary):
        """設置字體顏色（使用統一數據，避免重複計算範圍）"""
        try:
            from openpyxl.styles import Font

            # 直接使用統一數據中的範圍信息
            device_count = data_summary['device_count']
            item_count = data_summary['item_count']

            if device_count > 0 and item_count > 0:
                end_row = 12 + device_count
                end_col = 2 + item_count

                # 使用優化的批量字體設置
                self._batch_set_font_colors_super_optimized(ws, 13, end_row, 2, end_col)

                self.file_handler.log_message(f"設置數據區域字體顏色: 行13-{end_row}, 列2-{end_col}")

        except Exception as e:
            self.file_handler.log_message(f"設置字體顏色時出錯: {e}")

    def _add_site_fail_statistics_unified(self, summary_ws, data_summary, my_bin_array):
        """添加各Site的fail統計（使用統一數據）"""
        try:
            site_info = data_summary['site_info']
            if not site_info['found'] or site_info['total_site_no'] == 0:
                self.file_handler.log_message("沒有Site信息，跳過fail統計")
                return

            # 計算各Site的fail統計
            site_fail_stats = {}
            for device_row in data_summary['device_rows']:
                site_num = site_info['site_data'].get(device_row)
                if site_num:
                    bin_value = my_bin_array.get(device_row, 1)
                    if site_num not in site_fail_stats:
                        site_fail_stats[site_num] = {'total': 0, 'fail': 0}

                    site_fail_stats[site_num]['total'] += 1
                    if bin_value != 1:  # 假設Bin=1是Pass
                        site_fail_stats[site_num]['fail'] += 1

            # 在Summary中添加fail統計（可以添加到適當位置）
            row_offset = 3  # 在第3行開始添加統計
            summary_ws.cell(row_offset, 1).value = "Site Fail統計:"

            for site_num in sorted(site_fail_stats.keys()):
                stats = site_fail_stats[site_num]
                fail_rate = (stats['fail'] / stats['total'] * 100) if stats['total'] > 0 else 0

                row_offset += 1
                summary_ws.cell(row_offset, 1).value = f"Site {site_num}:"
                summary_ws.cell(row_offset, 2).value = f"Fail: {stats['fail']}/{stats['total']} ({fail_rate:.1f}%)"

            self.file_handler.log_message(f"添加了{len(site_fail_stats)}個Site的fail統計")

        except Exception as e:
            self.file_handler.log_message(f"添加Site fail統計時出錯: {e}")

    def _setup_summary_autofilter_and_sort(self, summary_ws):
        """設置Summary的AutoFilter和排序"""
        try:
            # 檢查是否有足夠的資料行（從第6行開始）
            if summary_ws.max_row > 6:
                from openpyxl.utils import get_column_letter

                # 動態獲取最大列數並轉換為字母
                max_column = summary_ws.max_column
                column_letter = get_column_letter(max_column)
                filter_range = f"A6:{column_letter}{summary_ws.max_row}"
                summary_ws.auto_filter.ref = filter_range
                self.file_handler.log_message(f"設置AutoFilter範圍: {filter_range}")

                # 按B欄由大到小排序（如果有數據）
                if summary_ws.max_row > 7:
                    data = []
                    for row in range(7, summary_ws.max_row + 1):
                        row_data = []
                        for col in range(1, summary_ws.max_column + 1):
                            row_data.append(summary_ws.cell(row, col).value)
                        if row_data[1] is not None:  # B欄有值才加入
                            data.append((row + 6, row_data))  # 保存原始行號

                    # 按B欄排序（索引1）
                    data.sort(key=lambda x: x[1][1] if x[1][1] is not None else 0, reverse=True)

                    # 重新寫入排序後的數據
                    for new_row, (original_row, row_data) in enumerate(data, start=7):
                        for col, value in enumerate(row_data, start=1):
                            summary_ws.cell(new_row, col).value = value

                    self.file_handler.log_message(f"按B欄排序完成，共{len(data)}行數據")

        except Exception as e:
            self.file_handler.log_message(f"設置AutoFilter和排序時出錯: {e}")

    def _analyze_device_with_full_logic_unified(self, device_row, data_summary, ws, max_limits, min_limits, my_tester_type):
        """執行設備的完整測試項目分析邏輯（使用統一數據，VBA 202-306行）"""
        device_failed_items = []
        item_positions = data_summary['item_positions']

        # 分析每個測試項目
        for col_index in item_positions:
            # 獲取測試值
            test_value = ws.cell(device_row, col_index).value
            if test_value is None:
                continue

            # 獲取限制值
            max_limit = max_limits.get(col_index)
            min_limit = min_limits.get(col_index)

            # 獲取項目的Bin號（第6行）
            item_bin = ws.cell(6, col_index).value
            try:
                item_bin = int(float(item_bin)) if item_bin is not None else 25
            except (ValueError, TypeError):
                item_bin = 25

            # 獲取項目名稱（第8行）
            item_name = ws.cell(8, col_index).value or f"Item_{col_index}"

            # VBA邏輯：檢查測試值是否為數字
            if not self._is_numeric(test_value, allow_tilde=True):
                continue

            try:
                numeric_value = float(test_value)
            except (ValueError, TypeError):
                continue

            # VBA邏輯：檢查是否超出限制
            failed = False
            if max_limit is not None and self._is_numeric(max_limit):
                try:
                    if numeric_value > float(max_limit):
                        failed = True
                except (ValueError, TypeError):
                    pass

            if min_limit is not None and self._is_numeric(min_limit):
                try:
                    if numeric_value < float(min_limit):
                        failed = True
                except (ValueError, TypeError):
                    pass

            if failed:
                device_failed_items.append({
                    'col': col_index,
                    'bin': item_bin,
                    'name': item_name,
                    'value': numeric_value,
                    'max_limit': max_limit,
                    'min_limit': min_limit
                })

        return device_failed_items

    def _apply_coloring_logic_unified(self, ws, failed_items, data_summary):
        """應用染色邏輯（使用統一數據，VBA 311-333行）"""
        try:
            from openpyxl.styles import Font

            for device_row, items in failed_items.items():
                for item in items:
                    col_index = item['col']
                    # 設置紅色字體
                    cell = ws.cell(device_row, col_index)
                    cell.font = Font(color="FF0000")

            self.file_handler.log_message(f"應用染色邏輯: {len(failed_items)}個設備的失敗項目")

        except Exception as e:
            self.file_handler.log_message(f"應用染色邏輯時出錯: {e}")

    def _add_original_file_link_unified(self, summary_ws, original_filename):
        """添加原始檔案超連結（統一版本）"""
        try:
            if original_filename:
                from openpyxl.worksheet.hyperlink import Hyperlink
                import os

                # 創建超連結到原始檔案
                cell = summary_ws.cell(1, 3)
                cell.value = f"原始檔案: {original_filename}"

                # 如果檔案存在，創建超連結
                if os.path.exists(original_filename):
                    cell.hyperlink = Hyperlink(ref=f"C1", target=original_filename)
                    # 設置超連結樣式
                    from openpyxl.styles import Font
                    cell.font = Font(color="0000FF", underline="single")
                    self.file_handler.log_message(f"添加原始檔案超連結: {original_filename}")
                else:
                    self.file_handler.log_message(f"添加原始檔案文字（檔案不存在）: {original_filename}")

        except Exception as e:
            self.file_handler.log_message(f"添加原始檔案連結時出錯: {e}")

    def _validate_worksheet_format(self, ws):
        """驗證工作表格式（檢查第12行是否有Serial#和Bin#）"""
        try:
            serial_cell = ws.cell(12, 1).value
            bin_cell = ws.cell(12, 2).value

            return (str(serial_cell) == "Serial#" and str(bin_cell) == "Bin#")
        except:
            return False

    def _get_original_format_type(self, ws):
        """獲取原始格式類型（由步驟5設定）"""
        try:
            format_value = ws.cell(9, 1).value
            format_str = str(format_value).upper() if format_value else ""

            self.file_handler.log_message(f"步驟7：讀取步驟5設定的原始格式標記: {format_str}")

            # 直接使用步驟5設定的標記，不再重複檢測
            if format_str in ["CTA", "TMT", "OTHER"]:
                return format_str
            else:
                self.file_handler.log_message(f"警告：未識別的格式標記 {format_str}，使用預設值 OTHER")
                return "OTHER"
        except:
            self.file_handler.log_message("錯誤：無法讀取格式標記，使用預設值 OTHER")
            return "OTHER"

    def _setup_excel_formatting(self, ws):
        """設置Excel格式（凍結窗格、自動篩選等）"""
        try:
            from openpyxl.worksheet.views import Pane

            # 設置凍結窗格（相當於VBA的FreezePanes）
            ws.freeze_panes = "C13"

            # 設置自動篩選（第12行）
            if ws.max_row >= 12 and ws.max_column >= 2:
                ws.auto_filter.ref = f"A12:{self._get_column_letter(ws.max_column)}12"

            self.file_handler.log_message("Excel格式設置完成：凍結窗格C13，自動篩選第12行")

        except Exception as e:
            self.file_handler.log_message(f"設置Excel格式時出錯: {e}")

    def _process_device_data(self, ws):
        """處理設備數據"""
        try:
            # 計算設備總數（從第13行開始的A列非空單元格）
            device_count = 0
            for row in range(13, ws.max_row + 1):
                if ws.cell(row, 1).value:
                    device_count += 1
                else:
                    break

            # 計算測試項目總數（第7行從C列開始的非空單元格）
            item_count = 0
            for col in range(3, ws.max_column + 1):
                if ws.cell(7, col).value:
                    item_count += 1
                else:
                    break

            self.file_handler.log_message(f"設備總數: {device_count}, 測試項目總數: {item_count}")

            # 處理第6行的項目編號（相當於VBA中的項目編號設置）
            self._set_item_numbers(ws, item_count)

            # 查找Site列（VBA第154-160行）
            self._find_site_column(ws, item_count)

        except Exception as e:
            self.file_handler.log_message(f"處理設備數據時出錯: {e}")

    def _set_item_numbers(self, ws, item_count):
        """設置第6行的項目編號（VBA第149行邏輯）"""
        try:
            # VBA: .Cells(6, myLoopItem).Value = myLoopItem + (myMaxPassBinN - 2) + myTotalItemNumber
            # 簡化實現：設置Bin編號
            my_max_pass_bin_n = 1  # 通常Pass Bin是1
            my_total_item_number = 0  # 簡化為0

            for col in range(3, 3 + item_count):
                my_loop_item = col
                bin_number = my_loop_item + (my_max_pass_bin_n - 2) + my_total_item_number
                ws.cell(6, col).value = bin_number

            self.file_handler.log_message(f"設置了{item_count}個項目編號")

        except Exception as e:
            self.file_handler.log_message(f"設置項目編號時出錯: {e}")

    def _find_site_column(self, ws, item_count):
        """查找Site列（VBA第154-160行）- 使用統一Site信息收集"""
        try:
            site_info = self._get_unified_site_info(ws, mode="simple")

            if site_info['found']:
                self.file_handler.log_message(f"找到Site列在第{site_info['site_column']}列")
                return site_info['site_column']
            else:
                self.file_handler.log_message("未找到Site列")
                return None

        except Exception as e:
            self.file_handler.log_message(f"查找Site列時出錯: {e}")
            return None

    def _execute_vba_core_analysis(self, ws, create_summary, output_format):
        """執行VBA核心分析（完整版本 - 基於VBA 159-361行）"""
        try:
            self.file_handler.log_message("開始VBA核心分析...")

            # 計算基本參數
            device_count = 0
            for row in range(13, ws.max_row + 1):
                if ws.cell(row, 1).value:
                    device_count += 1
                else:
                    break

            if device_count == 0:
                self.file_handler.log_message("沒有設備數據，跳過VBA核心分析")
                return

            # 子步驟5.1: 收集Site信息（VBA 159-168行）
            start_time = time.time()
            self.file_handler.log_message("🔄 步驟5.1：開始收集Site信息...")
            site_info = self._collect_site_info_from_original_ws(ws)
            self._log_timing("步驟5.1-收集Site信息", start_time)

            # 子步驟5.2: 收集Max/Min限制值（VBA 169-179行）
            start_time = time.time()
            self.file_handler.log_message("🔄 步驟5.2：開始收集Max/Min限制值...")
            max_limits, min_limits = self._collect_max_min_limits(ws)
            self._log_timing("步驟5.2-收集Max/Min限制值", start_time)

            # 子步驟5.3: 收集設備Bin信息（VBA 180-186行）
            start_time = time.time()
            self.file_handler.log_message("🔄 步驟5.3：開始收集設備Bin信息...")
            device_bins = self._collect_device_bins(ws)
            self._log_timing("步驟5.3-收集設備Bin信息", start_time)

            # 子步驟5.4: 分析設備測試結果（VBA 187-335行）
            start_time = time.time()
            self.file_handler.log_message("🔄 步驟5.4：開始分析設備測試結果...")
            analysis_result = self._analyze_device_test_results(ws, max_limits, min_limits, device_bins, output_format)
            # 將Site信息添加到分析結果中
            analysis_result['site_info'] = site_info
            self._log_timing("步驟5.4-分析設備測試結果", start_time)

            # 子步驟5.5: 統計Bin數據
            start_time = time.time()
            self.file_handler.log_message("🔄 步驟5.5：開始統計Bin數據...")
            bin_statistics = self._calculate_bin_statistics(analysis_result)
            self._log_timing("步驟5.5-統計Bin數據", start_time)

            # 子步驟5.6: 將計算出的設備Bin值寫回到第2列
            start_time = time.time()
            self.file_handler.log_message("🔄 步驟5.6：開始寫回設備Bin值...")
            self._write_device_bins_back(ws, analysis_result['device_bin_results'])
            self._log_timing("步驟5.6-寫回設備Bin值", start_time)

            # 子步驟5.7: 創建Summary工作表（僅Excel格式且需要Summary時）
            if create_summary and output_format.lower() == "excel":
                start_time = time.time()
                self.file_handler.log_message("🔄 步驟5.7：開始創建Summary工作表...")
                self._create_enhanced_summary_worksheet(ws, bin_statistics, analysis_result)
                self._log_timing("步驟5.7-創建Summary工作表", start_time)
            elif create_summary:
                self.file_handler.log_message("⏭️ 步驟5.7：跳過Summary工作表創建（CSV格式）")
            else:
                self.file_handler.log_message("⏭️ 步驟5.7：跳過Summary工作表創建（未要求創建）")

            self.file_handler.log_message("✅ 步驟5：VBA核心分析完成")

        except Exception as e:
            self.file_handler.log_message(f"執行VBA核心分析時出錯: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")

# 注意：_create_basic_summary方法已被_create_enhanced_summary_worksheet取代

    #@@@@@
    def _get_unified_site_info(self, ws, mode="full"):
        """統一的Site信息收集方法 - 整合所有重複的Site查找邏輯

        Args:
            ws: 工作表
            mode: "full" = 完整模式（包含設備行映射）, "simple" = 簡化模式（只統計數量）

        Returns:
            dict: 統一的Site信息結構
        """
        try:
            site_info = {
                'site_column': None,
                'site_header': None,
                'total_site_no': 0,
                'site_counts': {},  # {site_num: device_count}
                'site_data': {},    # {device_row: site_num} (僅full模式)
                'good_site_n': True,
                'found': False
            }

            # 統一的Site列查找邏輯 - 支持多種標題行位置
            search_rows = [8, 12]  # 第8行和第12行都可能是標題行

            for search_row in search_rows:
                for col in range(1, min(20, ws.max_column + 1)):
                    header = ws.cell(search_row, col).value
                    if header and ('site' in str(header).lower() or 'Site' in str(header)):
                        site_info['site_column'] = col
                        site_info['site_header'] = str(header)
                        site_info['found'] = True
                        self.file_handler.log_message(f"統一Site查找：在第{search_row}行第{col}列找到Site列，標題: {header}")
                        break

                if site_info['found']:
                    break

            if not site_info['found']:
                self.file_handler.log_message("統一Site查找：未找到Site列")
                return site_info

            # 統一的Site數據收集邏輯
            max_site_n = 10
            for device_row in range(13, ws.max_row + 1):
                site_value = ws.cell(device_row, site_info['site_column']).value

                if site_value is not None:
                    try:
                        # 統一的數值清理和轉換邏輯
                        cleaned_value = str(site_value).strip()
                        numeric_value = float(cleaned_value)

                        # 檢查是否為有效的整數Site號
                        if numeric_value < 1 or numeric_value != int(numeric_value):
                            site_info['good_site_n'] = False
                        elif numeric_value <= max_site_n:
                            site_num = int(numeric_value)

                            # 統計Site數量
                            site_info['site_counts'][site_num] = site_info['site_counts'].get(site_num, 0) + 1

                            # 更新最大Site號
                            if site_num > site_info['total_site_no']:
                                site_info['total_site_no'] = site_num

                            # 完整模式：記錄設備行映射
                            if mode == "full":
                                site_info['site_data'][device_row] = site_num

                    except (ValueError, TypeError):
                        site_info['good_site_n'] = False

            self.file_handler.log_message(f"統一Site收集完成: 總Site數={site_info['total_site_no']}, 設備數={sum(site_info['site_counts'].values())}, 有效={site_info['good_site_n']}")

            return site_info

        except Exception as e:
            self.file_handler.log_message(f"統一Site信息收集時出錯: {e}")
            return {
                'site_column': None,
                'site_header': None,
                'total_site_no': 0,
                'site_counts': {},
                'site_data': {},
                'good_site_n': False,
                'found': False
            }

    def _set_font_colors_without_overriding_red(self, ws):
        """設置字體顏色，但不覆蓋紅色字體（超級優化版）"""
        import time
        start_time = time.time()

        try:
            from openpyxl.styles import Font

            # 計算數據範圍
            device_count = 0
            for row in range(13, ws.max_row + 1):
                if ws.cell(row, 1).value:
                    device_count += 1
                else:
                    break

            item_count = 0
            for col in range(3, ws.max_column + 1):
                if ws.cell(7, col).value:
                    item_count += 1
                else:
                    break

            # 設置數據區域的字體顏色為黑色，但保留紅色字體
            if device_count > 0 and item_count > 0:
                end_row = 12 + device_count
                end_col = 2 + item_count

                # 超級優化1: 使用批量字體設置
                self._batch_set_font_colors_super_optimized(ws, 13, end_row, 2, end_col)

                self.file_handler.log_message(f"設置數據區域字體顏色（保留紅色）: 行13-{end_row}, 列2-{end_col}")

            # 記錄優化效果
            end_time = time.time()
            self.file_handler.log_message(f"⚡ 超級字體顏色優化完成，耗時{(end_time-start_time)*1000:.1f}ms")

        except Exception as e:
            self.file_handler.log_message(f"設置字體顏色時出錯: {e}")

    def _batch_set_font_colors_super_optimized(self, ws, start_row, end_row, start_col, end_col):
        """超級優化的批量字體顏色設置方法"""
        from openpyxl.styles import Font

        # 創建黑色字體樣式（只創建一次）
        black_font = Font(name="新細明體", size=12, color="000000")

        # 超級優化策略1: 預先收集所有紅色字體的位置
        red_font_positions = set()

        # 第一遍掃描：快速識別紅色字體位置（限制掃描範圍以提高速度）
        scan_rows = min(100, end_row - start_row + 1)  # 限制掃描行數
        for row in range(start_row, start_row + scan_rows):
            for col in range(start_col, min(start_col + 50, end_col + 1)):  # 限制掃描列數
                cell = ws.cell(row, col)
                if self._is_red_font_fast(cell):
                    red_font_positions.add((row, col))

        # 超級優化策略2: 按行批量處理，減少對象訪問
        processed_count = 0
        batch_size = 50  # 每批處理50個cell

        for row in range(start_row, end_row + 1):
            # 批量收集這一行需要處理的cell
            row_cells_to_process = []

            for col in range(start_col, end_col + 1):
                if (row, col) not in red_font_positions:
                    cell = ws.cell(row, col)
                    # 再次快速檢查是否為紅色（防止遺漏）
                    if not self._is_red_font_fast(cell):
                        row_cells_to_process.append(cell)

            # 批量設置這一行的字體
            for cell in row_cells_to_process:
                cell.font = black_font
                processed_count += 1

            # 每處理1000個cell記錄一次進度
            if processed_count % 1000 == 0:
                self.file_handler.log_message(f"⚡ 已處理{processed_count}個cell的字體顏色")

        self.file_handler.log_message(f"⚡ 超級批量字體設置完成，總共處理{processed_count}個cell")

    def _is_red_font_fast(self, cell):
        """快速檢查是否為紅色字體"""
        try:
            if cell.font and cell.font.color and hasattr(cell.font.color, 'rgb'):
                rgb_value = cell.font.color.rgb
                if rgb_value and ("FF0000" in str(rgb_value) or "FFFF0000" in str(rgb_value)):
                    return True
            return False
        except:
            return False

    # 注意：_get_total_device_number 方法已在 BaseProcessor 中統一實現

    def _collect_site_info_from_original_ws(self, ws):
        """從原始工作表收集Site信息 - 使用統一Site信息收集"""
        try:
            # 使用統一的Site信息收集方法
            unified_site_info = self._get_unified_site_info(ws, mode="full")

            # 轉換為舊格式以保持兼容性
            site_info = {
                'site_column_n': unified_site_info['site_column'],
                'total_site_no': unified_site_info['total_site_no'],
                'site_data': unified_site_info['site_data'],
                'good_site_n': unified_site_info['good_site_n']
            }

            self.file_handler.log_message(f"收集Site信息完成: 總Site數={site_info['total_site_no']}, 設備數={len(site_info['site_data'])}")
            return site_info

        except Exception as e:
            self.file_handler.log_message(f"收集Site信息時出錯: {e}")
            return {
                'site_column_n': None,
                'total_site_no': 0,
                'site_data': {},
                'good_site_n': False
            }

    def _collect_max_min_limits(self, ws):
        """收集Max/Min限制值（VBA 169-179行）"""
        self.file_handler.log_message("收集Max/Min限制值...")

        max_limits = {}
        min_limits = {}

        # 計算測試項目數量
        item_count = 0
        for col in range(3, ws.max_column + 1):
            if ws.cell(7, col).value:  # 第7行有測試項目編號
                item_count += 1
            else:
                break

        # 收集第10行（Max）和第11行（Min）的限制值
        for col in range(3, 3 + item_count):
            max_val = ws.cell(10, col).value
            min_val = ws.cell(11, col).value

            if max_val and str(max_val) != "none":
                try:
                    max_limits[col] = float(max_val)
                except (ValueError, TypeError):
                    pass

            if min_val and str(min_val) != "none":
                try:
                    min_limits[col] = float(min_val)
                except (ValueError, TypeError):
                    pass

        self.file_handler.log_message(f"收集到Max限制值: {len(max_limits)}個, Min限制值: {len(min_limits)}個")
        return max_limits, min_limits

    def _collect_device_bins(self, ws):
        """收集設備Bin信息（VBA 180-186行）"""
        self.file_handler.log_message("收集設備Bin信息...")

        device_bins = {}

        # 計算設備數量
        device_count = 0
        for row in range(13, ws.max_row + 1):
            if ws.cell(row, 1).value:
                device_count += 1
            else:
                break

        # 收集第2列的Bin信息
        for row in range(13, 13 + device_count):
            bin_value = ws.cell(row, 2).value
            if bin_value:
                device_bins[row] = bin_value

        self.file_handler.log_message(f"收集到{len(device_bins)}個設備的Bin信息")
        return device_bins

    def _analyze_device_test_results(self, ws, max_limits, min_limits, device_bins, output_format="excel"):
        """分析每個設備的測試結果（VBA 187-335行）"""
        self.file_handler.log_message("分析設備測試結果...")

        # 計算基本參數
        device_count = len(device_bins)
        item_count = 0
        for col in range(3, ws.max_column + 1):
            if ws.cell(7, col).value:
                item_count += 1
            else:
                break

        # 獲取原始格式類型（影響比較邏輯）
        original_format = self._get_original_format_type(ws)
        my_tester_type = 0 if original_format == "CTA" else 1  # CTA=0, 其他=1

        # VBA常數設定
        max_pass_bin_n = 4  # VBA: maxPassBinN = 4
        my_bin1 = False  # VBA: myBin1 = False (通常)

        # 初始化結果變量
        device_bin_results = {}  # 設備最終Bin結果
        failed_items = {}  # 失敗項目記錄

        # VBA邏輯：讀取原始Bin值（VBA 183行）
        original_bins = {}  # myBinN(myLoopDevice)
        for device_row in range(13, 13 + device_count):
            bin_value = device_bins.get(device_row, 25)
            # 確保是整數類型
            try:
                original_bins[device_row] = int(float(bin_value)) if bin_value is not None else 25
            except (ValueError, TypeError):
                original_bins[device_row] = 25

        # 分析每個設備（VBA 187-335行主循環）
        for device_row in range(13, 13 + device_count):
            device_failed_items = []

            # VBA邏輯：獲取設備的原始Bin值
            original_bin_value = original_bins[device_row]

            # VBA關鍵條件（行201）：If myBinN(myLoopDevice) > myMaxPassBinN Or myBin1 Then
            should_analyze = (original_bin_value > max_pass_bin_n) or my_bin1

            if should_analyze:
                # 執行完整的測試項目分析（VBA 202-306行）
                device_failed_items = self._analyze_device_with_full_logic(
                    device_row, item_count, ws, max_limits, min_limits, my_tester_type
                )

                # 根據失敗項目計算最終Bin值
                if device_failed_items:
                    # 使用第一個失敗項目的Bin值
                    device_bin_results[device_row] = device_failed_items[0]['bin']
                else:
                    # 沒有失敗項目，設為Pass Bin (1)
                    device_bin_results[device_row] = 1
            else:
                # VBA行308：直接使用原始Bin值
                device_bin_results[device_row] = original_bin_value
                device_failed_items = []  # 不分析則沒有失敗項目

            # 記錄失敗項目
            if device_failed_items:
                failed_items[device_row] = device_failed_items

        # 只有Excel格式才應用染色邏輯（VBA 311-333行）
        if output_format.lower() == "excel":
            self._apply_coloring_logic(ws, failed_items, item_count)
        else:
            self.file_handler.log_message("CSV格式輸出，跳過染色邏輯")

        # 返回分析結果
        return {
            'device_bin_results': device_bin_results,
            'failed_items': failed_items,
            'device_count': device_count,
            'item_count': item_count
        }

    def _analyze_device_with_full_logic(self, device_row, item_count, ws, max_limits, min_limits, my_tester_type):
        """執行設備的完整測試項目分析邏輯（VBA 202-306行）"""
        device_failed_items = []

        # 分析每個測試項目（VBA 202-306行）
        for col in range(3, 3 + item_count):
            # 獲取測試值
            test_value = ws.cell(device_row, col).value
            if test_value is None:
                continue

            # 獲取限制值
            max_limit = max_limits.get(col)
            min_limit = min_limits.get(col)

            # 獲取項目的Bin號（第6行）
            item_bin = ws.cell(6, col).value
            try:
                item_bin = int(float(item_bin)) if item_bin is not None else 25
            except (ValueError, TypeError):
                item_bin = 25

            # 獲取項目名稱（第8行）
            item_name = ws.cell(8, col).value or f"Item_{col}"

            # VBA邏輯：檢查測試值是否為數字
            if not self._is_numeric(test_value, allow_tilde=True):
                continue

            try:
                numeric_value = float(test_value)
            except (ValueError, TypeError):
                continue

            # VBA邏輯：比較測試值與限制值
            failed = False
            failure_reason = ""

            # 檢查Max限制
            if max_limit is not None:
                if my_tester_type == 0:  # CTA測試儀
                    if numeric_value > max_limit:
                        failed = True
                        failure_reason = f"超過Max限制 {max_limit}"
                else:  # 其他測試儀
                    if numeric_value >= max_limit:
                        failed = True
                        failure_reason = f"達到或超過Max限制 {max_limit}"

            # 檢查Min限制
            if min_limit is not None and not failed:
                if my_tester_type == 0:  # CTA測試儀
                    if numeric_value < min_limit:
                        failed = True
                        failure_reason = f"低於Min限制 {min_limit}"
                else:  # 其他測試儀
                    if numeric_value <= min_limit:
                        failed = True
                        failure_reason = f"達到或低於Min限制 {min_limit}"

            # 如果失敗，記錄失敗項目
            if failed:
                device_failed_items.append({
                    'column': col,
                    'item_name': item_name,
                    'test_value': numeric_value,
                    'bin': item_bin,
                    'reason': failure_reason
                })

        return device_failed_items

    def _apply_coloring_logic(self, ws, failed_items, item_count):
        """應用染色邏輯（VBA 311-333行）"""
        try:
            from openpyxl.styles import Font

            red_font = Font(color="FF0000")  # 紅色字體

            colored_count = 0
            for device_row, failed_list in failed_items.items():
                for failed_item in failed_list:
                    col = failed_item['column']
                    # 將失敗的測試值標記為紅色
                    ws.cell(device_row, col).font = red_font
                    colored_count += 1

            self.file_handler.log_message(f"應用染色邏輯：標記了{colored_count}個失敗測試值為紅色")

        except Exception as e:
            self.file_handler.log_message(f"應用染色邏輯時出錯: {e}")

    def _calculate_bin_statistics(self, analysis_result):
        """計算Bin統計數據"""
        try:
            device_bin_results = analysis_result['device_bin_results']
            device_count = analysis_result['device_count']

            # 統計每個Bin的設備數量
            bin_counts = {}
            for bin_value in device_bin_results.values():
                bin_counts[bin_value] = bin_counts.get(bin_value, 0) + 1

            # 計算Pass/Fail統計
            pass_count = bin_counts.get(1, 0)  # Bin 1通常是Pass
            fail_count = device_count - pass_count
            yield_rate = (pass_count / device_count * 100) if device_count > 0 else 0

            statistics = {
                'total_devices': device_count,
                'pass_devices': pass_count,
                'fail_devices': fail_count,
                'yield': yield_rate,
                'bin_counts': bin_counts
            }

            self.file_handler.log_message(f"統計完成: 總設備{device_count}, Pass{pass_count}, Fail{fail_count}, 良率{self._format_percentage(yield_rate)}")
            return statistics

        except Exception as e:
            self.file_handler.log_message(f"計算Bin統計時出錯: {e}")
            return {
                'total_devices': 0,
                'pass_devices': 0,
                'fail_devices': 0,
                'yield': 0,
                'bin_counts': {}
            }

    def _write_device_bins_back(self, ws, device_bin_results):
        """將計算出的設備Bin值寫回到第2列（VBA第352-361行）"""
        try:
            updated_count = 0

            for device_row, bin_value in device_bin_results.items():
                # VBA: .Cells(myLoopDevice, 2).Value = myDeviceBinN(myLoopDevice - 1)
                ws.cell(device_row, 2).value = bin_value
                updated_count += 1

            self.file_handler.log_message(f"更新了{updated_count}個設備的Bin值")

        except Exception as e:
            self.file_handler.log_message(f"寫回設備Bin值時出錯: {e}")

    def _create_enhanced_summary_worksheet(self, ws, bin_statistics, analysis_result):
        """創建增強的Summary工作表（基於VBA 362-420行邏輯）"""
        try:
            self.file_handler.log_message("創建增強Summary工作表...")

            # 獲取工作簿
            wb = ws.parent

            # VBA 362行：檢查myTotalDeviceNumber
            total_device_number = self._get_total_device_number(ws)
            if not total_device_number:
                self.file_handler.log_message("沒有設備數據，跳過Summary創建")
                return

            # VBA 363-367行：創建Summary工作表
            if "Summary" in wb.sheetnames:
                summary_ws = wb["Summary"]
                wb.remove(summary_ws)

            summary_ws = wb.create_sheet("Summary")

            # VBA 457-469行：填充A1~C4的基本統計信息（必須在其他內容之前）
            self._fill_summary_basic_info(summary_ws, bin_statistics, total_device_number)

            # 添加原始檔案連結到C1（在基本統計信息之後）
            original_filename = getattr(self, 'original_filename', None)
            self._add_original_file_link(summary_ws, ws, original_filename)

            # VBA 375-378行：填充Bin 1 (All Pass)
            my_bin_array = analysis_result.get('device_bin_results', {})

            # 計算Bin 1的設備數量
            bin1_count = sum(1 for bin_val in my_bin_array.values() if bin_val == 1)

            summary_ws.cell(7, 1).value = 1
            summary_ws.cell(7, 2).value = bin1_count
            percentage = bin1_count / total_device_number * 100
            # 設置為數字格式的百分比
            percentage_cell = summary_ws.cell(7, 3)
            percentage_cell.value = self._get_percentage_value(percentage)  # 小數值
            percentage_cell.number_format = '0.00%'  # Excel百分比格式
            summary_ws.cell(7, 4).value = "All Pass"

            # 添加標題行（第6行）- VBA篩選需要
            self._set_summary_headers(summary_ws)

            # VBA 387-420行：處理所有測試項目的Bin數據
            self._fill_test_item_bins(summary_ws, ws, my_bin_array, total_device_number, analysis_result)

            # VBA 421-448行：設置標題行和Site統計
            # 從analysis_result中獲取Site信息，如果沒有則重新收集
            site_info = analysis_result.get('site_info')
            if not site_info:
                site_info = self._collect_site_info_from_original_ws(ws)
            self._setup_summary_headers_and_site_stats_vba_421_448(summary_ws, ws, my_bin_array, total_device_number, analysis_result, site_info)

            # 應用VBA風格的格式化（包含篩選和排序）
            self._apply_vba_style_formatting(summary_ws)

            self.file_handler.log_message("增強Summary工作表創建完成")

        except Exception as e:
            self.file_handler.log_message(f"創建增強Summary工作表時出錯: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")

    def _fill_summary_basic_info(self, summary_ws, bin_statistics, total_device_number):
        """填充Summary工作表A1~C4的基本統計信息（基於VBA 457-469行邏輯）"""
        try:
            # VBA 457-462行：填充統計數據
            # A1: "Total", B1: 總設備數
            summary_ws.cell(1, 1).value = "Total"
            summary_ws.cell(1, 2).value = total_device_number

            # A2: "Pass", B2: Pass設備數
            pass_count = bin_statistics.get('pass_devices', 0)
            summary_ws.cell(2, 1).value = "Pass"
            summary_ws.cell(2, 2).value = pass_count

            # A3: "Fail", B3: Fail設備數
            fail_count = bin_statistics.get('fail_devices', 0)
            summary_ws.cell(3, 1).value = "Fail"
            summary_ws.cell(3, 2).value = fail_count

            # VBA 463-469行：計算Yield（修正：*100加%）
            # A4: "Yield", B4: 良率百分比
            if total_device_number > 0:
                yield_value = pass_count / total_device_number * 100  # *100轉換為百分比
                summary_ws.cell(4, 1).value = "Yield"
                # 設置為數字格式的百分比
                yield_cell = summary_ws.cell(4, 2)
                yield_cell.value = self._get_percentage_value(yield_value)  # 小數值
                yield_cell.number_format = '0.00%'  # Excel百分比格式
            else:
                summary_ws.cell(4, 1).value = "Yield"
                yield_cell = summary_ws.cell(4, 2)
                yield_cell.value = 0.0  # 數字0
                yield_cell.number_format = '0.00%'  # Excel百分比格式

            self.file_handler.log_message(f"VBA 457-469行：填充基本統計 Total={total_device_number}, Pass={pass_count}, Fail={fail_count}, Yield={self._format_percentage(yield_value if total_device_number > 0 else 0)}")

        except Exception as e:
            self.file_handler.log_message(f"填充基本統計信息時出錯: {e}")

    def _set_summary_headers(self, summary_ws):
        """設置Summary標題行（統一方法避免重複）"""
        summary_ws.cell(6, 1).value = "Bin"
        summary_ws.cell(6, 2).value = "Count"
        summary_ws.cell(6, 3).value = "%"
        summary_ws.cell(6, 4).value = "Definition"
        summary_ws.cell(6, 5).value = "Note"

    def _add_original_file_link(self, summary_ws, ws, original_filename=None):
        """在Summary工作表C1添加原始檔案連結"""
        try:
            # 如果沒有提供原始檔案名，嘗試從其他地方獲取
            if not original_filename:
                wb = ws.parent

                # 方法1: 檢查工作簿的自定義屬性
                if hasattr(wb, 'custom_doc_props'):
                    try:
                        for prop in wb.custom_doc_props:
                            if prop.name == 'OriginalFilename':
                                original_filename = prop.value
                                break
                    except:
                        pass

                # 方法2: 檢查工作表中是否包含檔案信息
                if not original_filename:
                    # 嘗試從Data11工作表的某個位置獲取檔案信息
                    for row in range(1, 10):
                        for col in range(1, 10):
                            cell_value = ws.cell(row, col).value
                            if cell_value and isinstance(cell_value, str):
                                if '.csv' in cell_value.lower() or '.spd' in cell_value.lower():
                                    original_filename = cell_value
                                    break
                        if original_filename:
                            break

            # 如果找到原始檔案名，創建連結
            if original_filename:
                import os
                filename_only = os.path.basename(original_filename)
                summary_ws.cell(1, 3).value = f"原始檔案: {filename_only}"

                # 嘗試創建實際的檔案連結（如果檔案存在）
                if os.path.exists(original_filename):
                    summary_ws.cell(1, 3).hyperlink = original_filename
                    summary_ws.cell(1, 3).style = "Hyperlink"
                    self.file_handler.log_message(f"已在Summary C1添加原始檔案連結: {filename_only}")
                else:
                    self.file_handler.log_message(f"已在Summary C1添加原始檔案名: {filename_only} (檔案不存在，無連結)")
            else:
                # 如果沒有找到原始檔案名，添加一個通用標記
                summary_ws.cell(1, 3).value = "原始檔案: [檔案名]"
                self.file_handler.log_message("已在Summary C1添加原始檔案標記（未找到具體檔案名）")

        except Exception as e:
            self.file_handler.log_message(f"添加原始檔案連結時出錯: {e}")
            # 即使出錯也添加一個基本標記
            try:
                summary_ws.cell(1, 3).value = "原始檔案連結"
            except:
                pass

    def _fill_test_item_bins(self, summary_ws, ws, my_bin_array, total_device_number, analysis_result):
        """填充測試項目、數量、良率（修正版：避免重複填充）"""
        try:
            # 計算每個Bin的設備數量
            bin_counts = {}
            for device_bin in my_bin_array.values():
                bin_counts[device_bin] = bin_counts.get(device_bin, 0) + 1

            # 收集所有有效的測試項目信息（避免重複）
            bin_items = {}  # {bin_num: item_name}

            # 首先確保All Pass (Bin 1) 總是被包含
            if 1 in bin_counts and bin_counts[1] > 0:
                bin_items[1] = "All Pass"

            for col in range(3, ws.max_column + 1):
                my_bin = ws.cell(6, col).value  # 第6行是Bin號
                my_item_name = ws.cell(8, col).value  # 第8行是項目名稱

                if my_bin is not None and my_item_name is not None:
                    try:
                        bin_num = int(float(my_bin))
                        item_name = str(my_item_name).strip()

                        # 只保留第一次遇到的項目名稱（避免重複）
                        if bin_num not in bin_items:
                            bin_items[bin_num] = item_name

                    except (ValueError, TypeError):
                        pass

            # 按順序填充Summary數據（從第7行開始）
            current_row = 7
            total_item_number = 0

            # 按Bin號排序填充（確保順序一致）
            for bin_num in sorted(bin_items.keys()):
                bin_count = bin_counts.get(bin_num, 0)

                # 只填充有設備的Bin
                if bin_count > 0:
                    item_name = bin_items[bin_num]

                    summary_ws.cell(current_row, 1).value = bin_num
                    summary_ws.cell(current_row, 2).value = bin_count
                    percentage = bin_count / total_device_number * 100
                    # 設置為數字格式的百分比
                    percentage_cell = summary_ws.cell(current_row, 3)
                    percentage_cell.value = self._get_percentage_value(percentage)  # 小數值
                    percentage_cell.number_format = '0.00%'  # Excel百分比格式
                    summary_ws.cell(current_row, 4).value = item_name

                    current_row += 1
                    total_item_number += 1

            self.file_handler.log_message(f"VBA 362-420行（修正版）：填充了{total_item_number}個測試項目的Bin數據，無重複")

        except Exception as e:
            self.file_handler.log_message(f"填充測試項目Bin數據時出錯: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")

    def _setup_summary_headers_and_site_stats_vba_421_448(self, summary_ws, ws, my_bin_array, total_device_number, analysis_result, site_info=None):
        """設置Summary標題行和Site統計（VBA 421-448行邏輯，支持預收集Site信息）"""
        try:
            # VBA 421行：mySummarySheet.Activate（在Python中不需要）

            # VBA 422-426行：設置標題行（第6行）
            self._set_summary_headers(summary_ws)
            # Note列暫時不設置，讓Site數據從第5列開始

            # 計算實際的項目數量（Definition列固定在第4列）
            actual_item_number = self._calculate_actual_item_number(summary_ws, 4)

            # VBA 427-448行：處理Site統計信息
            if site_info is not None:
                # 使用預收集的Site信息（推薦方式）
                self.file_handler.log_message(f"Site信息檢查: total_site_no={site_info['total_site_no']}, good_site_n={site_info['good_site_n']}, site_data數量={len(site_info['site_data'])}")

                if site_info['total_site_no'] > 0 and site_info['good_site_n']:
                    site_stats = self._create_site_statistics_from_info(site_info, my_bin_array)
                    self._fill_site_statistics_vba_427_448(summary_ws, site_stats, total_device_number, actual_item_number)
                else:
                    self.file_handler.log_message(f"沒有有效的Site數據，跳過Site統計。原因: total_site_no={site_info['total_site_no']}, good_site_n={site_info['good_site_n']}")

            self.file_handler.log_message(f"VBA 421-448行：設置標題行和Site統計完成，實際項目數量: {actual_item_number}")

        except Exception as e:
            self.file_handler.log_message(f"設置Summary標題行和Site統計時出錯: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")

    def _calculate_actual_item_number(self, summary_ws, definition_col=None):
        """計算實際的項目數量（VBA中的myTotalItemNumber）"""
        try:
            # 使用傳入的Definition列位置，如果沒有則使用D列作為後備
            if definition_col is not None:
                target_col = definition_col
                col_name = f"第{definition_col}列(Definition列)"
            else:
                # 後備方案：使用D列
                if summary_ws.max_column >= 4:
                    target_col = 4
                    col_name = "第4列(D列-後備)"
                    self.file_handler.log_message("警告：沒有指定Definition列，使用D列作為後備")
                else:
                    self.file_handler.log_message("錯誤：沒有指定Definition列且工作表列數不足")
                    return 0

            # 計算指定列中有多少個項目（從第7行開始，因為前面是基本統計和標題）
            item_count = 0
            for row in range(7, summary_ws.max_row + 1):
                cell_value = summary_ws.cell(row, target_col).value
                if cell_value is not None and str(cell_value).strip():
                    item_count += 1
                else:
                    break  # 遇到空值就停止計算

            self.file_handler.log_message(f"計算實際{col_name}項目數量: {item_count}")
            return item_count

        except Exception as e:
            self.file_handler.log_message(f"計算實際項目數量時出錯: {e}")
            return 0  # 找不到就返回0，不執行後續操作

    def _create_site_statistics_from_info(self, site_info, my_bin_array):
        """從收集的Site信息創建統計數據"""
        try:
            site_stats = {
                'total_site_no': site_info['total_site_no'],
                'good_site_n': site_info['good_site_n'],
                'site_bin_array': {},
                'site_total_device_no': {}
            }

            # 初始化所有Site的統計
            for site_num in range(1, site_info['total_site_no'] + 1):
                site_stats['site_bin_array'][site_num] = {}
                site_stats['site_total_device_no'][site_num] = 0

            # 根據Site信息和設備Bin分配統計每個Site的Bin數據
            for device_row, site_num in site_info['site_data'].items():
                device_bin = my_bin_array.get(device_row, 1)  # 默認Bin 1

                # 統計Site的Bin數據
                if device_bin not in site_stats['site_bin_array'][site_num]:
                    site_stats['site_bin_array'][site_num][device_bin] = 0
                site_stats['site_bin_array'][site_num][device_bin] += 1

                # 統計Site的總設備數
                site_stats['site_total_device_no'][site_num] += 1

            # 收集所有實際存在的Bin號
            existing_bins = set(my_bin_array.values())

            # 為每個Site的每個實際存在的Bin設置統計（如果該Site沒有該Bin的設備，則為0）
            for site_num in range(1, site_info['total_site_no'] + 1):
                for bin_num in existing_bins:
                    if bin_num not in site_stats['site_bin_array'][site_num]:
                        site_stats['site_bin_array'][site_num][bin_num] = 0

            self.file_handler.log_message(f"創建Site統計完成: {site_info['total_site_no']}個Site")
            for site_num in range(1, site_info['total_site_no'] + 1):
                total_devices = site_stats['site_total_device_no'][site_num]
                self.file_handler.log_message(f"  Site {site_num}: {total_devices}個設備")

            return site_stats

        except Exception as e:
            self.file_handler.log_message(f"創建Site統計時出錯: {e}")
            return {
                'total_site_no': 0,
                'good_site_n': False,
                'site_bin_array': {},
                'site_total_device_no': {}
            }

    def _fill_site_statistics_vba_427_448(self, summary_ws, site_stats, total_device_number, actual_item_number):
        """填充Site統計數據（VBA 427-448行邏輯）"""
        try:
            total_site_no = site_stats['total_site_no']
            site_bin_array = site_stats['site_bin_array']
            site_total_device_no = site_stats['site_total_device_no']

            self.file_handler.log_message(f"開始填充Site統計: {total_site_no}個Site, 實際項目數量: {actual_item_number}")

            # 動態處理所有Site（不限制數量，基於實際存在的Site）
            for site_index in sorted(site_bin_array.keys()):
                # 計算Site對應的列位置
                col_count, col_percent = self._calculate_site_columns(site_index)

                # 設置Site標題（第5行和第6行）
                site_device_count = site_total_device_no.get(site_index, 0)
                self._set_site_headers(summary_ws, site_index, col_count, col_percent, site_device_count)

                # VBA 433行：填充Bin 1數據（第7行）
                bin1_count = site_bin_array.get(site_index, {}).get(1, 0)
                summary_ws.cell(7, col_count).value = bin1_count
                if site_device_count > 0 and bin1_count > 0:
                    percentage_value = bin1_count / site_device_count * 100
                    # 設置為數字格式的百分比
                    percentage_cell = summary_ws.cell(7, col_percent)
                    percentage_cell.value = self._get_percentage_value(percentage_value)  # 小數值
                    percentage_cell.number_format = '0.00%'  # Excel百分比格式
                else:
                    percentage_cell = summary_ws.cell(7, col_percent)
                    percentage_cell.value = 0.0  # 數字0
                    percentage_cell.number_format = '0.00%'  # Excel百分比格式

                # 遍歷Summary工作表中實際存在的行，比對測項填入Site統計
                max_row = summary_ws.max_row
                for row in range(7, max_row + 1):  # 從第7行開始檢查所有行
                    # 檢查該行是否有Bin數據（第1列有值）
                    bin_value = summary_ws.cell(row, 1).value
                    if bin_value is None:
                        continue

                    try:
                        bin_num = int(float(bin_value))

                        # 從site_bin_array中獲取該Bin在該Site的統計數量
                        site_bin_count = site_bin_array.get(site_index, {}).get(bin_num, 0)

                        # 填充Site統計數量
                        summary_ws.cell(row, col_count).value = site_bin_count

                        # 計算並填充百分比
                        if site_device_count > 0 and site_bin_count > 0:
                            percentage_value = site_bin_count / site_device_count * 100
                            # 設置為數字格式的百分比
                            percentage_cell = summary_ws.cell(row, col_percent)
                            percentage_cell.value = self._get_percentage_value(percentage_value)  # 小數值
                            percentage_cell.number_format = '0.00%'  # Excel百分比格式
                        else:
                            percentage_cell = summary_ws.cell(row, col_percent)
                            percentage_cell.value = 0.0  # 數字0
                            percentage_cell.number_format = '0.00%'  # Excel百分比格式

                    except (ValueError, TypeError):
                        # 如果Bin值無法轉換為數字，跳過該行
                        continue

                self.file_handler.log_message(f"填充Site {site_index}統計完成: {site_device_count}個設備")

        except Exception as e:
            self.file_handler.log_message(f"填充Site統計時出錯: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")

    def _calculate_site_columns(self, site_index):
        """計算Site對應的列位置"""
        # VBA邏輯：4 + 2 * site_index, 5 + 2 * site_index
        col_count = 4 + 2 * site_index
        col_percent = 5 + 2 * site_index
        return col_count, col_percent

    def _set_site_headers(self, summary_ws, site_index, col_count, col_percent, site_total_devices):
        """設置Site標題（統一方法避免重複）"""
        summary_ws.cell(5, col_count).value = "Total"
        summary_ws.cell(5, col_percent).value = site_total_devices
        summary_ws.cell(6, col_count).value = f"Site {site_index}"
        summary_ws.cell(6, col_percent).value = "%"

    def _apply_vba_style_formatting(self, summary_ws):
        """應用VBA風格的格式化（VBA 449-475行）"""
        try:
            from openpyxl.styles import Font
            from openpyxl.utils import get_column_letter

            # 檢查是否有足夠的資料行（從第6行開始）
            if summary_ws.max_row > 6:
                # 動態獲取最大列數並轉換為字母
                max_column = summary_ws.max_column
                column_letter = get_column_letter(max_column)
                filter_range = f"A6:{column_letter}{summary_ws.max_row}"
                summary_ws.auto_filter.ref = filter_range
                self.file_handler.log_message(f"設置 AutoFilter 範圍: {filter_range}")

                # 篩選完直接排列：按B欄由大到小排序
                data = []
                for row in range(7, summary_ws.max_row + 1):
                    row_data = []
                    for col in range(1, summary_ws.max_column + 1):
                        row_data.append(summary_ws.cell(row, col).value)
                    if row_data[1] is not None:  # B欄有值才加入
                        data.append(row_data)

                if data:
                    # 按第2欄（index=1，即B欄）排序
                    data.sort(key=lambda x: x[1] if isinstance(x[1], (int, float)) else 0, reverse=True)

                    # 寫回工作表
                    for i, row_data in enumerate(data):
                        row = 7 + i
                        for col, value in enumerate(row_data, 1):
                            summary_ws.cell(row, col).value = value

                    self.file_handler.log_message(f"按 B 欄由大到小排序了 {len(data)} 行資料")

            else:
                self.file_handler.log_message("資料行數不足（<= 6），未設置 AutoFilter 和排序")

            # 自動調整D列（Definition列）的寬度
            self._auto_adjust_definition_column_width(summary_ws)

        except Exception as e:
            self.file_handler.log_message(f"應用VBA風格格式化時出錯: {e}")

    def _auto_adjust_definition_column_width(self, summary_ws):
        """自動調整D列（Definition列）的寬度以適應內容"""
        try:
            from openpyxl.utils import get_column_letter

            # D列是第4列
            definition_col = 4
            column_letter = get_column_letter(definition_col)

            # 計算D列內容的最大寬度
            max_width = 0

            # 檢查標題行（第6行）
            header_cell = summary_ws.cell(6, definition_col)
            if header_cell.value:
                header_length = len(str(header_cell.value))
                max_width = max(max_width, header_length)

            # 檢查數據行（從第7行開始）
            for row in range(7, summary_ws.max_row + 1):
                cell = summary_ws.cell(row, definition_col)
                if cell.value:
                    content_length = len(str(cell.value))
                    max_width = max(max_width, content_length)

            # 設置列寬，考慮字體寬度和一些額外空間
            # 中文字符通常需要更多空間，所以使用較大的係數
            if max_width > 0:
                # 基本寬度計算：每個字符約1.2個單位，最小寬度10，最大寬度50
                calculated_width = max(10, min(50, max_width * 1.2 + 2))
                summary_ws.column_dimensions[column_letter].width = calculated_width

                self.file_handler.log_message(f"自動調整D列寬度: {calculated_width:.1f} (基於最大內容長度: {max_width})")
            else:
                # 如果沒有內容，設置預設寬度
                summary_ws.column_dimensions[column_letter].width = 12
                self.file_handler.log_message("D列沒有內容，設置預設寬度: 12")

        except Exception as e:
            self.file_handler.log_message(f"自動調整D列寬度時出錯: {e}")
