#!/usr/bin/env python3
"""
FillEmptyItemName處理器模組
"""

import time
from openpyxl.styles import Font
from .base_processor import BaseProcessor
from .processor_utils import _get_total_device_number_common
from config.settings import FILL_EMPTY_SETTINGS, PROCESSING_LIMITS


class FillEmptyItemNameProcessor(BaseProcessor):
    """FillEmptyItemName處理器"""

    def __init__(self):
        super().__init__()

    def apply_processing(self, ws):
        """應用FillEmptyItemName處理 - 第二階段處理（統一優化版）"""
        overall_start = time.time()
        self.file_handler.log_message("🚀 第二階段：開始FillEmptyItemName處理（統一優化版）...")

        # 步驟1: 計算總測試項目數量（使用快速模式）
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟1：開始計算總測試項目數量（快速模式）...")
        my_total_item_number = self._calculate_total_item_number_optimized(ws)
        self._log_timing("步驟1-計算總測試項目數量", start_time)
        self.file_handler.log_message(f"✅ 步驟1：總測試項目數量: {my_total_item_number}")

        # 步驟2: 填充空的測試項目名稱和編號
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟2：開始填充測試項目名稱和編號...")
        self._fill_test_item_names(ws, my_total_item_number)
        self._log_timing("步驟2-填充測試項目名稱和編號", start_time)

        # 步驟3: 填充Min/Max值
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟3：開始填充Min/Max值...")
        self._fill_min_max_values(ws, my_total_item_number)
        self._log_timing("步驟3-填充Min/Max值", start_time)

        # 步驟4: 跳過設備行清理（統一優化）
        self.file_handler.log_message("⏭️ 步驟4：跳過設備行清理（統一優化）")

        self._log_timing("第二階段FillEmptyItemName處理總時間", overall_start)
        self.file_handler.log_message("✅ 第二階段：FillEmptyItemName處理完成")

    def _calculate_total_item_number_optimized(self, ws):
        """計算總測試項目數量（統一優化版）"""
        # 統一使用最有效率的快速模式：直接使用工作表的最大列數
        result = max(ws.max_column - 2, PROCESSING_LIMITS['MAX_COLUMNS'] - 2)
        self.file_handler.log_message(f"使用統一優化模式計算項目數量: {result}")
        return result

    def _calculate_total_item_number(self, ws, fast_mode=False):
        """計算總測試項目數量（大文件優化版）"""
        if fast_mode:
            # 快速模式：對於SPD檔案，直接使用工作表的最大列數
            return max(ws.max_column - 2, PROCESSING_LIMITS['MAX_COLUMNS'] - 2)

        # 優化1: 使用更智能的搜索策略
        # 大多數GMT文件的測試項目都在前200列內，先快速掃描
        max_search_col = max(ws.max_column + 1, PROCESSING_LIMITS['MAX_COLUMNS'])

        # 優化2: 批量讀取單元格值，減少單個單元格訪問
        my_total_item_number = self._count_non_empty_cells_optimized(ws, 12, 3, max_search_col)

        # 優化3: 如果第12行計算結果合理，跳過第8行的額外檢查
        if my_total_item_number > 0 and my_total_item_number < 3000:
            # 只對可疑結果進行第8行的額外檢查
            return my_total_item_number

        # 如果第12行結果不合理，才進行第8行的詳細檢查
        return self._fallback_item_count_check(ws, my_total_item_number)

    def _count_non_empty_cells_optimized(self, ws, row, start_col, max_col):
        """
        優化的非空單元格計數方法

        使用批量訪問和早期退出策略來提升性能
        """
        count = 0
        # 優化：批量檢查，每次檢查10個單元格
        batch_size = 10

        for col_start in range(start_col, max_col, batch_size):
            col_end = min(col_start + batch_size, max_col)

            # 批量檢查這一批單元格
            has_value_in_batch = False
            for col in range(col_start, col_end):
                if ws.cell(row, col).value:
                    count += 1
                    has_value_in_batch = True
                else:
                    # 如果遇到空值，檢查是否應該停止
                    if not has_value_in_batch and col > start_col + 50:
                        # 如果已經檢查了50列以上且這批都是空的，可能已經到達末尾
                        return count
                    elif not has_value_in_batch:
                        # 如果這批開始就是空的，直接返回
                        return count
                    else:
                        # 遇到空值但這批有其他值，繼續檢查
                        break

            # 如果這批完全沒有值，可能已經到達末尾
            if not has_value_in_batch:
                break

        return count

    def _fallback_item_count_check(self, ws, initial_count):
        """
        備用的項目計數檢查方法

        當第12行的結果不合理時使用
        """
        # 限制檢查範圍，避免無限循環
        check_range = min(128, ws.max_column - initial_count - 3)

        for i in range(initial_count + 3, initial_count + 3 + check_range):
            if i <= ws.max_column:
                if not ws.cell(8, i).value:
                    return i - 3

        return initial_count

    def _fill_test_item_names(self, ws, my_total_item_number):
        """填充測試項目名稱和編號"""
        pre_item_n = 0
        sub_item_n = 0

        for my_item_index in range(1, my_total_item_number + 1):
            col_index = my_item_index + 2

            # 檢查第12行且第8行是否有值
            if (ws.cell(12, col_index).value and ws.cell(8, col_index).value):

                # 如果第7行為空，填充測試項目編號
                if not ws.cell(7, col_index).value:
                    if my_item_index < 10:
                        ws.cell(7, col_index).value = FILL_EMPTY_SETTINGS['SIMPLE_FORMAT'].format(my_item_index)
                    else:
                        ws.cell(7, col_index).value = f"0.00.{my_item_index}"

                    # 設置字體顏色為紅色
                    ws.cell(7, col_index).font = Font(name="新細明體", size=12, color="FF0000")

                    # 如果第8行為空，填充測試項目名稱
                    if not ws.cell(8, col_index).value:
                        cell_12_value = str(ws.cell(12, col_index).value or "")
                        if my_item_index != 1 or "Time" not in cell_12_value:
                            ws.cell(8, col_index).value = ws.cell(12, col_index).value
                        else:
                            ws.cell(8, col_index).value = FILL_EMPTY_SETTINGS['TEST_TIME_NAME']

                        # 設置字體顏色為紅色
                        ws.cell(8, col_index).font = Font(name="新細明體", size=12, color="FF0000")

                # 處理數字格式的測試項目編號
                elif self._is_numeric_or_tilde(ws.cell(7, col_index).value):
                    cell_value = str(ws.cell(7, col_index).value)

                    # 處理波浪號
                    if "~" in cell_value:
                        cell_value = cell_value.replace("~", ".")

                    test_array = cell_value.split(".")

                    if len(test_array) == 2:
                        cur_item_n = int(test_array[0])
                        if cur_item_n != pre_item_n:
                            sub_item_n = 1
                        else:
                            sub_item_n += 1

                        formatted_value = self._format_item_number(cur_item_n, sub_item_n)
                        ws.cell(7, col_index).value = formatted_value
                        pre_item_n = cur_item_n

                    elif len(test_array) == 1:
                        cur_item_n = pre_item_n
                        if my_item_index == 1:
                            try:
                                sub_item_n = int(test_array[0])
                            except ValueError:
                                sub_item_n = 1
                        else:
                            sub_item_n += 1

                        formatted_value = self._format_item_number(cur_item_n, sub_item_n)
                        ws.cell(7, col_index).value = formatted_value
                else:
                    break

    def _is_numeric_or_tilde(self, value):
        """檢查值是否為數字或包含波浪號"""
        return self._is_numeric(value, allow_tilde=True)

    def _format_item_number(self, cur_item_n, sub_item_n):
        """格式化測試項目編號"""
        return FILL_EMPTY_SETTINGS['ITEM_NUMBER_FORMAT'].format(cur_item_n, sub_item_n)

    def _fill_min_max_values(self, ws, my_total_item_number):
        """填充Min/Max值"""
        for my_item_index in range(1, my_total_item_number + 1):
            col_index = my_item_index + 2

            if ws.cell(12, col_index).value:
                # 檢查第10行（Max值）是否為空
                if not str(ws.cell(10, col_index).value or "").strip():
                    ws.cell(10, col_index).value = FILL_EMPTY_SETTINGS['DEFAULT_MAX_VALUE']
                    ws.cell(10, col_index).font = Font(name="新細明體", size=12, color="FF0000")

                # 檢查第11行（Min值）是否為空
                if not str(ws.cell(11, col_index).value or "").strip():
                    ws.cell(11, col_index).value = FILL_EMPTY_SETTINGS['DEFAULT_MIN_VALUE']
                    ws.cell(11, col_index).font = Font(name="新細明體", size=12, color="FF0000")

    def _clean_device_rows(self, ws):
        """清理多餘的設備行"""
        my_total_device_number = self._get_total_device_number(ws)
        self.file_handler.log_message(f"總設備數量: {my_total_device_number}")

        # 檢查是否有無效的設備行
        for i in range(1, my_total_device_number + 1):
            row_index = 12 + i
            if row_index > ws.max_row:
                break

            cell_value = ws.cell(row_index, 1).value

            if not cell_value or not self._is_numeric(cell_value):
                # 找到無效行，需要刪除後續的無效行
                invalid_rows_count = my_total_device_number - i + 1
                start_row = 12 + i
                end_row = start_row

                for j in range(start_row, min(PROCESSING_LIMITS['MAX_ROWS'], ws.max_row + 1)):
                    if j > ws.max_row:
                        break

                    if ws.cell(j, 1).value:
                        invalid_rows_count -= 1
                    if invalid_rows_count == 0:
                        end_row = j
                        break

                # 刪除無效行
                if end_row >= start_row and end_row <= ws.max_row:
                    ws.delete_rows(start_row, end_row - start_row + 1)
                    self.file_handler.log_message(f"刪除了第{start_row}到第{end_row}行的無效設備數據")
                break

    def _get_total_device_number(self, ws):
        """計算總設備數量"""
        return _get_total_device_number_common(ws, PROCESSING_LIMITS['MAX_DEVICE_ROWS'])
