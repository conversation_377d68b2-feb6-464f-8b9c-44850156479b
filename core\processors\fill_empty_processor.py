#!/usr/bin/env python3
"""
FillEmptyItemName處理器模組
"""

import time
from openpyxl.styles import Font
from .base_processor import BaseProcessor

from config.settings import FILL_EMPTY_SETTINGS, PROCESSING_LIMITS


class FillEmptyItemNameProcessor(BaseProcessor):
    """FillEmptyItemName處理器 - 專門負責步驟6：數據填充（MAX MIN、測試項目名稱等）"""

    def __init__(self):
        super().__init__()

    def apply_processing(self, ws):
        """應用FillEmptyItemName處理 - 步驟6：數據填充（統一優化版）

        Returns:
            dict: 統一的數據摘要，供步驟7使用
        """
        overall_start = time.time()
        self.file_handler.log_message("🚀 步驟6：開始數據填充處理（統一優化版）...")

        # 步驟1: 統一數據收集（一次性收集所有基礎數據）
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟1：開始統一數據收集...")
        data_summary = self.collect_unified_data_summary(ws)
        self._log_timing("步驟1-統一數據收集", start_time)

        # 步驟2: 填充空的測試項目名稱和編號
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟2：開始填充測試項目名稱和編號...")
        self._fill_test_item_names(ws, data_summary)
        self._log_timing("步驟2-填充測試項目名稱和編號", start_time)

        # 步驟3: 填充Min/Max值
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟3：開始填充Min/Max值...")
        self._fill_min_max_values(ws, data_summary)
        self._log_timing("步驟3-填充Min/Max值", start_time)

        # 步驟4: 跳過設備行清理（統一優化）
        self.file_handler.log_message("⏭️ 步驟4：跳過設備行清理（統一優化）")

        self._log_timing("步驟6 數據填充處理總時間", overall_start)
        self.file_handler.log_message("✅ 步驟6：數據填充處理完成，數據摘要已準備好供步驟7使用")

        return data_summary

    # 注意：_calculate_total_item_number_optimized 已整合到統一數據收集中

    def _fill_test_item_names(self, ws, data_summary):
        """填充測試項目名稱和編號（使用統一數據）"""
        pre_item_n = 0
        sub_item_n = 0
        my_total_item_number = data_summary['item_count']

        for my_item_index in range(1, my_total_item_number + 1):
            col_index = my_item_index + 2

            # 檢查第12行且第8行是否有值
            if (ws.cell(12, col_index).value and ws.cell(8, col_index).value):

                # 如果第7行為空，填充測試項目編號
                if not ws.cell(7, col_index).value:
                    if my_item_index < 10:
                        ws.cell(7, col_index).value = FILL_EMPTY_SETTINGS['SIMPLE_FORMAT'].format(my_item_index)
                    else:
                        ws.cell(7, col_index).value = f"0.00.{my_item_index}"

                    # 設置字體顏色為紅色
                    ws.cell(7, col_index).font = Font(name="新細明體", size=12, color="FF0000")

                    # 如果第8行為空，填充測試項目名稱
                    if not ws.cell(8, col_index).value:
                        cell_12_value = str(ws.cell(12, col_index).value or "")
                        if my_item_index != 1 or "Time" not in cell_12_value:
                            ws.cell(8, col_index).value = ws.cell(12, col_index).value
                        else:
                            ws.cell(8, col_index).value = FILL_EMPTY_SETTINGS['TEST_TIME_NAME']

                        # 設置字體顏色為紅色
                        ws.cell(8, col_index).font = Font(name="新細明體", size=12, color="FF0000")

                # 處理數字格式的測試項目編號（使用統一方法）
                elif self._is_numeric_or_tilde(ws.cell(7, col_index).value):
                    cell_value = str(ws.cell(7, col_index).value)

                    # 處理波浪號
                    if "~" in cell_value:
                        cell_value = cell_value.replace("~", ".")

                    test_array = cell_value.split(".")

                    if len(test_array) == 2:
                        cur_item_n = int(test_array[0])
                        if cur_item_n != pre_item_n:
                            sub_item_n = 1
                        else:
                            sub_item_n += 1

                        formatted_value = self._format_item_number(cur_item_n, sub_item_n)
                        ws.cell(7, col_index).value = formatted_value
                        pre_item_n = cur_item_n

                    elif len(test_array) == 1:
                        cur_item_n = pre_item_n
                        if my_item_index == 1:
                            try:
                                sub_item_n = int(test_array[0])
                            except ValueError:
                                sub_item_n = 1
                        else:
                            sub_item_n += 1

                        formatted_value = self._format_item_number(cur_item_n, sub_item_n)
                        ws.cell(7, col_index).value = formatted_value
                else:
                    break

    # 注意：_is_numeric_or_tilde 和 _format_item_number 方法已移到 BaseProcessor 中

    def _fill_min_max_values(self, ws, data_summary):
        """填充Min/Max值（使用統一數據）"""
        my_total_item_number = data_summary['item_count']
        for my_item_index in range(1, my_total_item_number + 1):
            col_index = my_item_index + 2

            if ws.cell(12, col_index).value:
                # 檢查第10行（Max值）是否為空
                if not str(ws.cell(10, col_index).value or "").strip():
                    ws.cell(10, col_index).value = FILL_EMPTY_SETTINGS['DEFAULT_MAX_VALUE']
                    ws.cell(10, col_index).font = Font(name="新細明體", size=12, color="FF0000")

                # 檢查第11行（Min值）是否為空
                if not str(ws.cell(11, col_index).value or "").strip():
                    ws.cell(11, col_index).value = FILL_EMPTY_SETTINGS['DEFAULT_MIN_VALUE']
                    ws.cell(11, col_index).font = Font(name="新細明體", size=12, color="FF0000")


