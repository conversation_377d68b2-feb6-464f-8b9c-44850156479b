#!/usr/bin/env python3
"""
FillEmptyItemName處理器模組
"""

import time
from openpyxl.styles import Font
from .base_processor import BaseProcessor

from config.settings import FILL_EMPTY_SETTINGS, PROCESSING_LIMITS


class FillEmptyItemNameProcessor(BaseProcessor):
    """FillEmptyItemName處理器 - 專門負責步驟6：數據填充（MAX MIN、測試項目名稱等）"""

    def __init__(self):
        super().__init__()

    def apply_processing(self, ws):
        """應用FillEmptyItemName處理 - 步驟6：數據填充（統一優化版）

        Returns:
            dict: 統一的數據摘要，供步驟7使用
        """
        overall_start = time.time()
        self.file_handler.log_message("🚀 步驟6：開始數據填充處理（統一優化版）...")

        # 步驟1: 統一數據收集（一次性收集所有基礎數據）
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟1：開始統一數據收集...")
        data_summary = self.collect_unified_data_summary(ws)
        self._log_timing("步驟1-統一數據收集", start_time)

        # 6.2 填充測試項目名稱和編號
        start_time = time.time()
        self.file_handler.log_message("🔄 6.2 填充測試項目名稱和編號...")
        self.file_handler.log_message("  - 檢查第7行、第8行是否有項目信息")
        self.file_handler.log_message("  - 填充缺失的項目編號和名稱")
        self.file_handler.log_message("  - 設置紅色字體標記")
        self._fill_test_item_names(ws, data_summary)
        self._log_timing("6.2-填充測試項目名稱和編號", start_time)

        # 6.3 填充Min/Max值
        start_time = time.time()
        self.file_handler.log_message("🔄 6.3 填充Min/Max值...")
        self.file_handler.log_message("  - 檢查第10行（Max值）和第11行（Min值）")
        self.file_handler.log_message("  - 填充預設限制值")
        self.file_handler.log_message("  - 設置紅色字體標記")
        self._fill_min_max_values(ws, data_summary)
        self._log_timing("6.3-填充Min/Max值", start_time)

        # 6.4 數據驗證與整理
        self.file_handler.log_message("🔄 6.4 數據驗證與整理...")
        self.file_handler.log_message("  - 驗證數據完整性")
        self.file_handler.log_message("  - 準備統一數據摘要")
        self.file_handler.log_message("  - 重新計算並更新Data11分頁B列Bin數值")

        # 6.5 重新計算並更新Data11分頁B列Bin數值（從步驟7移過來）
        start_time = time.time()
        self.file_handler.log_message("🔄 6.5 重新計算並更新Data11分頁B列Bin數值...")
        self._recalculate_and_update_device_bins(ws, data_summary)
        self._log_timing("6.5-重新計算並更新Bin數值", start_time)

        self._log_timing("步驟6 數據填充與統一收集總時間", overall_start)
        self.file_handler.log_message("✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用")

        return data_summary

    def _recalculate_and_update_device_bins(self, ws, data_summary):
        """重新計算並更新Data11分頁B列Bin數值（從步驟7移過來的功能）"""
        try:
            self.file_handler.log_message("開始重新計算設備Bin值...")

            # 使用統一數據中的設備信息
            device_rows = data_summary['device_rows']
            device_bins = data_summary['device_bins']

            # 獲取限制值
            max_limits = data_summary['max_limits']
            min_limits = data_summary['min_limits']

            # 重新計算每個設備的Bin值
            updated_bins = {}
            for device_row in device_rows:
                # 分析設備的測試結果
                original_bin = device_bins.get(device_row, 1)

                # 檢查是否有測試項目失敗
                has_failed_items = False
                for col in range(3, 3 + data_summary['item_count']):
                    test_value = ws.cell(device_row, col).value
                    if test_value is not None:
                        try:
                            test_val = float(test_value)
                            max_val = max_limits.get(col, float('inf'))
                            min_val = min_limits.get(col, float('-inf'))

                            # 檢查是否超出限制
                            if test_val > max_val or test_val < min_val:
                                has_failed_items = True
                                break
                        except (ValueError, TypeError):
                            continue

                # 根據測試結果決定Bin值
                if has_failed_items:
                    # 有失敗項目，使用失敗Bin值（通常是2或更高）
                    new_bin = max(2, original_bin)
                else:
                    # 沒有失敗項目，使用Pass Bin值（通常是1）
                    new_bin = 1

                updated_bins[device_row] = new_bin

            # 將計算出的Bin值寫回到B列
            updated_count = 0
            for device_row, bin_value in updated_bins.items():
                ws.cell(device_row, 2).value = bin_value
                updated_count += 1

            # 更新data_summary中的device_bins
            data_summary['device_bins'].update(updated_bins)

            self.file_handler.log_message(f"重新計算並更新了{updated_count}個設備的Bin值")

        except Exception as e:
            self.file_handler.log_message(f"重新計算設備Bin值時出錯: {e}")

    # 注意：_calculate_total_item_number_optimized 已整合到統一數據收集中

    def _fill_test_item_names(self, ws, data_summary):
        """填充測試項目名稱和編號（使用統一數據）"""
        pre_item_n = 0
        sub_item_n = 0
        my_total_item_number = data_summary['item_count']

        for my_item_index in range(1, my_total_item_number + 1):
            col_index = my_item_index + 2

            # 檢查第12行且第8行是否有值
            if (ws.cell(12, col_index).value and ws.cell(8, col_index).value):

                # 如果第7行為空，填充測試項目編號
                if not ws.cell(7, col_index).value:
                    if my_item_index < 10:
                        ws.cell(7, col_index).value = FILL_EMPTY_SETTINGS['SIMPLE_FORMAT'].format(my_item_index)
                    else:
                        ws.cell(7, col_index).value = f"0.00.{my_item_index}"

                    # 設置字體顏色為紅色
                    ws.cell(7, col_index).font = Font(name="新細明體", size=12, color="FF0000")

                    # 如果第8行為空，填充測試項目名稱
                    if not ws.cell(8, col_index).value:
                        cell_12_value = str(ws.cell(12, col_index).value or "")
                        if my_item_index != 1 or "Time" not in cell_12_value:
                            ws.cell(8, col_index).value = ws.cell(12, col_index).value
                        else:
                            ws.cell(8, col_index).value = FILL_EMPTY_SETTINGS['TEST_TIME_NAME']

                        # 設置字體顏色為紅色
                        ws.cell(8, col_index).font = Font(name="新細明體", size=12, color="FF0000")

                # 處理數字格式的測試項目編號（使用統一方法）
                elif self._is_numeric_or_tilde(ws.cell(7, col_index).value):
                    cell_value = str(ws.cell(7, col_index).value)

                    # 處理波浪號
                    if "~" in cell_value:
                        cell_value = cell_value.replace("~", ".")

                    test_array = cell_value.split(".")

                    if len(test_array) == 2:
                        cur_item_n = int(test_array[0])
                        if cur_item_n != pre_item_n:
                            sub_item_n = 1
                        else:
                            sub_item_n += 1

                        formatted_value = self._format_item_number(cur_item_n, sub_item_n)
                        ws.cell(7, col_index).value = formatted_value
                        pre_item_n = cur_item_n

                    elif len(test_array) == 1:
                        cur_item_n = pre_item_n
                        if my_item_index == 1:
                            try:
                                sub_item_n = int(test_array[0])
                            except ValueError:
                                sub_item_n = 1
                        else:
                            sub_item_n += 1

                        formatted_value = self._format_item_number(cur_item_n, sub_item_n)
                        ws.cell(7, col_index).value = formatted_value
                else:
                    break

    # 注意：_is_numeric_or_tilde 和 _format_item_number 方法已移到 BaseProcessor 中

    def _fill_min_max_values(self, ws, data_summary):
        """填充Min/Max值（使用統一數據）"""
        my_total_item_number = data_summary['item_count']
        for my_item_index in range(1, my_total_item_number + 1):
            col_index = my_item_index + 2

            if ws.cell(12, col_index).value:
                # 檢查第10行（Max值）是否為空
                if not str(ws.cell(10, col_index).value or "").strip():
                    ws.cell(10, col_index).value = FILL_EMPTY_SETTINGS['DEFAULT_MAX_VALUE']
                    ws.cell(10, col_index).font = Font(name="新細明體", size=12, color="FF0000")

                # 檢查第11行（Min值）是否為空
                if not str(ws.cell(11, col_index).value or "").strip():
                    ws.cell(11, col_index).value = FILL_EMPTY_SETTINGS['DEFAULT_MIN_VALUE']
                    ws.cell(11, col_index).font = Font(name="新細明體", size=12, color="FF0000")


