#!/usr/bin/env python3
"""
處理器共用工具函數模組
"""


def _get_total_device_number_common(ws, max_rows=None):
    """統一的設備數量計算方法

    Args:
        ws: 工作表
        max_rows: 最大行數限制（可選）

    Returns:
        int: 設備總數
    """
    device_count = 0
    if max_rows:
        max_row = min(ws.max_row + 1, max_rows)
    else:
        max_row = ws.max_row + 1

    for row in range(13, max_row):
        if ws.cell(row, 1).value:
            device_count += 1
        else:
            break
    return device_count
