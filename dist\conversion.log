2025-07-23 17:40:18,688 - INFO - 開始完整轉換...
2025-07-23 17:40:18,692 - INFO - 讀取CSV文件: C:/Users/<USER>/Desktop/test/GMT_G2304.csv
2025-07-23 17:40:18,722 - INFO - 成功讀取CSV文件，大小: (3511, 60)
2025-07-23 17:40:18,724 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-23 17:40:18,975 - INFO - ⚡ 優化寫入完成: 3484行 × 60列，耗時248.0ms
2025-07-23 17:40:18,978 - INFO - ⚡ 優化寫入完成: 22行 × 60列，耗時3.0ms
2025-07-23 17:40:18,979 - INFO - ⚡ 優化寫入完成: 4行 × 60列，耗時1.0ms
2025-07-23 17:40:18,979 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時252.9ms
2025-07-23 17:40:18,981 - INFO - 應用CTA8280格式...
2025-07-23 17:40:18,981 - INFO - 🚀 第一階段：開始CTA8280格式處理...
2025-07-23 17:40:18,981 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-23 17:40:18,981 - INFO - 找到標題行在第2行
2025-07-23 17:40:18,981 - INFO - ⏱️ 步驟1-找到標題行 執行時間: <1ms
2025-07-23 17:40:18,981 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-23 17:40:18,981 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: <1ms
2025-07-23 17:40:18,981 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-23 17:40:18,982 - INFO - myFindedRowN=2, myColumnN=14
2025-07-23 17:40:18,987 - INFO - ⚡ 超級批量插入6行完成，耗時1.0ms
2025-07-23 17:40:18,987 - INFO - 插入了6行在最前面
2025-07-23 17:40:18,987 - INFO - ⚡ 標題行插入優化完成，耗時1.0ms
2025-07-23 17:40:18,987 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 6.0ms
2025-07-23 17:40:18,987 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-23 17:40:18,987 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-23 17:40:18,987 - INFO - 清空位置 A8: "Index_No"
2025-07-23 17:40:18,987 - INFO - 清空位置 B8: "Dut_No"
2025-07-23 17:40:18,987 - INFO - 清空位置 A10: "ASD"
2025-07-23 17:40:18,987 - INFO - 清空位置 B10: "QQ"
2025-07-23 17:40:18,987 - INFO - 清空位置 A11: "A"
2025-07-23 17:40:18,987 - INFO - 清空位置 B11: "B"
2025-07-23 17:40:18,987 - INFO - ✅ 清空了6個多餘資料位置
2025-07-23 17:40:18,988 - INFO - 最後一列資料密度正常(31.0%)，保留資料
2025-07-23 17:40:18,988 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 1.0ms
2025-07-23 17:40:18,988 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-23 17:40:18,989 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: 1.0ms
2025-07-23 17:40:18,989 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-23 17:40:18,989 - INFO - 找到SW_Bin列在第6列
2025-07-23 17:40:18,989 - INFO - 處理了16個設備的數據
2025-07-23 17:40:18,989 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: <1ms
2025-07-23 17:40:18,989 - INFO - ⏱️ 第一階段CTA8280格式處理總時間 執行時間: 8.0ms
2025-07-23 17:40:18,990 - INFO - ✅ 第一階段：CTA8280格式處理完成
2025-07-23 17:40:18,990 - INFO - 應用FillEmptyItemName處理...
2025-07-23 17:40:18,990 - INFO - 🚀 第二階段：開始FillEmptyItemName處理...
2025-07-23 17:40:18,990 - INFO - 🔄 步驟1：開始計算總測試項目數量...
2025-07-23 17:40:18,990 - INFO - ⏱️ 步驟1-計算總測試項目數量 執行時間: <1ms
2025-07-23 17:40:18,990 - INFO - ✅ 步驟1：總測試項目數量: 58
2025-07-23 17:40:18,990 - INFO - 🔄 步驟2：開始填充測試項目名稱和編號...
2025-07-23 17:40:18,990 - INFO - ⏱️ 步驟2-填充測試項目名稱和編號 執行時間: <1ms
2025-07-23 17:40:18,990 - INFO - 🔄 步驟3：開始填充Min/Max值...
2025-07-23 17:40:18,991 - INFO - ⏱️ 步驟3-填充Min/Max值 執行時間: 1.0ms
2025-07-23 17:40:18,991 - INFO - 🔄 步驟4：開始清理設備行...
2025-07-23 17:40:18,991 - INFO - 總設備數量: 16
2025-07-23 17:40:18,992 - INFO - ⏱️ 步驟4-清理設備行 執行時間: <1ms
2025-07-23 17:40:18,992 - INFO - ⏱️ 第二階段FillEmptyItemName處理總時間 執行時間: 2.0ms
2025-07-23 17:40:18,992 - INFO - ✅ 第二階段：FillEmptyItemName處理完成
2025-07-23 17:40:18,992 - INFO - 應用Device2BinControl處理...
2025-07-23 17:40:18,992 - INFO - 🚀 第三階段：開始Device2BinControl處理...
2025-07-23 17:40:18,992 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-23 17:40:18,992 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-23 17:40:18,992 - INFO - 🔄 步驟2：開始測試儀類型檢測...
2025-07-23 17:40:18,992 - INFO - ⏱️ 步驟2-測試儀類型檢測 執行時間: <1ms
2025-07-23 17:40:18,992 - INFO - ✅ 步驟2：檢測到測試儀類型: CTA
2025-07-23 17:40:18,992 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-23 17:40:18,992 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-23 17:40:18,992 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: <1ms
2025-07-23 17:40:18,992 - INFO - 🔄 步驟4：開始設備數據處理...
2025-07-23 17:40:18,993 - INFO - 設備總數: 16, 測試項目總數: 58
2025-07-23 17:40:18,993 - INFO - 設置了58個項目編號
2025-07-23 17:40:18,993 - INFO - 統一Site查找：在第8行第4列找到Site列，標題: Site_No
2025-07-23 17:40:18,993 - INFO - 統一Site收集完成: 總Site數=4, 設備數=16, 有效=False
2025-07-23 17:40:18,993 - INFO - 找到Site列在第4列
2025-07-23 17:40:18,993 - INFO - ⏱️ 步驟4-設備數據處理 執行時間: 1.0ms
2025-07-23 17:40:18,993 - INFO - 🔄 步驟5：開始VBA核心分析...
2025-07-23 17:40:18,993 - INFO - 開始VBA核心分析...
2025-07-23 17:40:18,993 - INFO - 🔄 步驟5.1：開始收集Site信息...
2025-07-23 17:40:18,993 - INFO - 統一Site查找：在第8行第4列找到Site列，標題: Site_No
2025-07-23 17:40:18,993 - INFO - 統一Site收集完成: 總Site數=4, 設備數=16, 有效=False
2025-07-23 17:40:18,993 - INFO - 收集Site信息完成: 總Site數=4, 設備數=16
2025-07-23 17:40:18,993 - INFO - ⏱️ 步驟5.1-收集Site信息 執行時間: <1ms
2025-07-23 17:40:18,993 - INFO - 🔄 步驟5.2：開始收集Max/Min限制值...
2025-07-23 17:40:18,993 - INFO - 收集Max/Min限制值...
2025-07-23 17:40:18,993 - INFO - 收集到Max限制值: 43個, Min限制值: 43個
2025-07-23 17:40:18,993 - INFO - ⏱️ 步驟5.2-收集Max/Min限制值 執行時間: <1ms
2025-07-23 17:40:18,993 - INFO - 🔄 步驟5.3：開始收集設備Bin信息...
2025-07-23 17:40:18,993 - INFO - 收集設備Bin信息...
2025-07-23 17:40:18,993 - INFO - 收集到16個設備的Bin信息
2025-07-23 17:40:18,993 - INFO - ⏱️ 步驟5.3-收集設備Bin信息 執行時間: <1ms
2025-07-23 17:40:18,994 - INFO - 🔄 步驟5.4：開始分析設備測試結果...
2025-07-23 17:40:18,994 - INFO - 分析設備測試結果...
2025-07-23 17:40:18,995 - INFO - 應用染色邏輯：標記了12個失敗測試值為紅色
2025-07-23 17:40:18,995 - INFO - ⏱️ 步驟5.4-分析設備測試結果 執行時間: <1ms
2025-07-23 17:40:18,995 - INFO - 🔄 步驟5.5：開始統計Bin數據...
2025-07-23 17:40:18,995 - INFO - 統計完成: 總設備16, Pass4, Fail12, 良率25.00%
2025-07-23 17:40:18,995 - INFO - ⏱️ 步驟5.5-統計Bin數據 執行時間: <1ms
2025-07-23 17:40:18,995 - INFO - 🔄 步驟5.6：開始寫回設備Bin值...
2025-07-23 17:40:18,995 - INFO - 更新了16個設備的Bin值
2025-07-23 17:40:18,995 - INFO - ⏱️ 步驟5.6-寫回設備Bin值 執行時間: <1ms
2025-07-23 17:40:18,995 - INFO - 🔄 步驟5.7：開始創建Summary工作表...
2025-07-23 17:40:18,996 - INFO - 創建增強Summary工作表...
2025-07-23 17:40:18,996 - INFO - VBA 457-469行：填充基本統計 Total=16, Pass=4, Fail=12, Yield=25.00%
2025-07-23 17:40:18,996 - INFO - 已在Summary C1添加原始檔案連結: GMT_G2304.csv
2025-07-23 17:40:18,996 - INFO - VBA 362-420行（修正版）：填充了2個測試項目的Bin數據，無重複
2025-07-23 17:40:18,996 - INFO - 計算實際第4列(Definition列)項目數量: 2
2025-07-23 17:40:18,997 - INFO - Site信息檢查: total_site_no=4, good_site_n=False, site_data數量=16
2025-07-23 17:40:18,997 - INFO - 沒有有效的Site數據，跳過Site統計。原因: total_site_no=4, good_site_n=False
2025-07-23 17:40:18,997 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 2
2025-07-23 17:40:18,997 - INFO - 設置 AutoFilter 範圍: A6:E8
2025-07-23 17:40:18,997 - INFO - 按 B 欄由大到小排序了 2 行資料
2025-07-23 17:40:18,997 - INFO - 自動調整D列寬度: 14.0 (基於最大內容長度: 10)
2025-07-23 17:40:18,997 - INFO - 增強Summary工作表創建完成
2025-07-23 17:40:18,997 - INFO - ⏱️ 步驟5.7-創建Summary工作表 執行時間: 2.0ms
2025-07-23 17:40:18,997 - INFO - ✅ 步驟5：VBA核心分析完成
2025-07-23 17:40:18,997 - INFO - ⏱️ 步驟5-VBA核心分析 執行時間: 4.0ms
2025-07-23 17:40:18,997 - INFO - 🔄 步驟6：開始字體顏色設置...
2025-07-23 17:40:19,008 - INFO - ⚡ 超級批量字體設置完成，總共處理932個cell
2025-07-23 17:40:19,008 - INFO - 設置數據區域字體顏色（保留紅色）: 行13-28, 列2-60
2025-07-23 17:40:19,008 - INFO - ⚡ 超級字體顏色優化完成，耗時11.0ms
2025-07-23 17:40:19,008 - INFO - ⏱️ 步驟6-字體顏色設置 執行時間: 11.0ms
2025-07-23 17:40:19,008 - INFO - ⏱️ 第三階段Device2BinControl處理總時間 執行時間: 16.0ms
2025-07-23 17:40:19,008 - INFO - ✅ 第三階段：Device2BinControl處理完成
2025-07-23 17:40:19,968 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-23 17:40:20,225 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\dist\GMT_G2304_clean_converted.xlsx，耗時257.0ms
2025-07-23 17:40:20,225 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\dist\GMT_G2304_clean_converted.xlsx
2025-07-23 17:40:25,962 - INFO - 開始解壓縮ZIP檔案: C:/Users/<USER>/Desktop/test/G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd.zip
2025-07-23 17:40:25,963 - INFO - 創建臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_5pczgsyr
2025-07-23 17:40:25,963 - INFO - ZIP檔案包含: ['G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd']
2025-07-23 17:40:25,988 - INFO - 成功解壓縮檔案: C:\Users\<USER>\AppData\Local\Temp\csv_converter_5pczgsyr\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd
2025-07-23 17:40:27,103 - INFO - 清理臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_5pczgsyr
2025-07-23 17:40:28,695 - INFO - 開始解壓縮ZIP檔案: C:/Users/<USER>/Desktop/test/G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd.zip
2025-07-23 17:40:28,696 - INFO - 創建臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_xthesx9t
2025-07-23 17:40:28,696 - INFO - ZIP檔案包含: ['G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd']
2025-07-23 17:40:28,721 - INFO - 成功解壓縮檔案: C:\Users\<USER>\AppData\Local\Temp\csv_converter_xthesx9t\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd
2025-07-23 17:40:29,234 - INFO - 開始完整轉換...
2025-07-23 17:40:29,234 - INFO - 檢測到SPD檔案，直接轉換為Excel...
2025-07-23 17:40:29,234 - INFO - 讀取CSV文件: C:\Users\<USER>\AppData\Local\Temp\csv_converter_xthesx9t\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd
2025-07-23 17:40:29,423 - INFO - 成功讀取CSV文件，大小: (7971, 110)
2025-07-23 17:40:29,423 - INFO - SPD檔案直接轉換，已是Data11格式
2025-07-23 17:40:31,380 - INFO - ⚡ 優化寫入完成: 7971行 × 110列，耗時1955.9ms
2025-07-23 17:40:31,384 - INFO - SPD檔案：在9A位置寫入TMT...
2025-07-23 17:40:31,385 - INFO - 應用FillEmptyItemName處理...
2025-07-23 17:40:31,385 - INFO - 🚀 第二階段：開始FillEmptyItemName處理...
2025-07-23 17:40:31,385 - INFO - 🔄 步驟1：開始計算總測試項目數量...
2025-07-23 17:40:31,415 - INFO - ⏱️ 步驟1-計算總測試項目數量 執行時間: 30.0ms
2025-07-23 17:40:31,415 - INFO - ✅ 步驟1：總測試項目數量: 298
2025-07-23 17:40:31,415 - INFO - 🔄 步驟2：開始填充測試項目名稱和編號...
2025-07-23 17:40:31,416 - INFO - ⏱️ 步驟2-填充測試項目名稱和編號 執行時間: 1.0ms
2025-07-23 17:40:31,416 - INFO - 🔄 步驟3：開始填充Min/Max值...
2025-07-23 17:40:31,416 - INFO - ⏱️ 步驟3-填充Min/Max值 執行時間: <1ms
2025-07-23 17:40:31,416 - INFO - ⏭️ 步驟4：跳過設備行清理（SPD檔案優化）
2025-07-23 17:40:31,416 - INFO - ⏱️ 第二階段FillEmptyItemName處理總時間 執行時間: 31.0ms
2025-07-23 17:40:31,416 - INFO - ✅ 第二階段：FillEmptyItemName處理完成
2025-07-23 17:40:31,416 - INFO - 應用Device2BinControl處理...
2025-07-23 17:40:31,416 - INFO - 🚀 第三階段：開始Device2BinControl處理...
2025-07-23 17:40:31,416 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-23 17:40:31,416 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-23 17:40:31,416 - INFO - 🔄 步驟2：開始測試儀類型檢測...
2025-07-23 17:40:31,416 - INFO - ⏱️ 步驟2-測試儀類型檢測 執行時間: <1ms
2025-07-23 17:40:31,416 - INFO - ✅ 步驟2：檢測到測試儀類型: TMT
2025-07-23 17:40:31,416 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-23 17:40:31,494 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-23 17:40:31,494 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 78.0ms
2025-07-23 17:40:31,494 - INFO - 🔄 步驟4：開始設備數據處理...
2025-07-23 17:40:31,547 - INFO - 設備總數: 7959, 測試項目總數: 108
2025-07-23 17:40:31,547 - INFO - 設置了108個項目編號
2025-07-23 17:40:31,579 - INFO - 統一Site查找：在第8行第4列找到Site列，標題: Site
2025-07-23 17:40:31,605 - INFO - 統一Site收集完成: 總Site數=2, 設備數=7959, 有效=True
2025-07-23 17:40:31,605 - INFO - 找到Site列在第4列
2025-07-23 17:40:31,605 - INFO - ⏱️ 步驟4-設備數據處理 執行時間: 110.9ms
2025-07-23 17:40:31,605 - INFO - 🔄 步驟5：開始VBA核心分析...
2025-07-23 17:40:31,605 - INFO - 開始VBA核心分析...
2025-07-23 17:40:31,626 - INFO - 🔄 步驟5.1：開始收集Site信息...
2025-07-23 17:40:31,654 - INFO - 統一Site查找：在第8行第4列找到Site列，標題: Site
2025-07-23 17:40:31,677 - INFO - 統一Site收集完成: 總Site數=2, 設備數=7959, 有效=True
2025-07-23 17:40:31,677 - INFO - 收集Site信息完成: 總Site數=2, 設備數=7959
2025-07-23 17:40:31,677 - INFO - ⏱️ 步驟5.1-收集Site信息 執行時間: 51.0ms
2025-07-23 17:40:31,677 - INFO - 🔄 步驟5.2：開始收集Max/Min限制值...
2025-07-23 17:40:31,677 - INFO - 收集Max/Min限制值...
2025-07-23 17:40:31,706 - INFO - 收集到Max限制值: 57個, Min限制值: 56個
2025-07-23 17:40:31,706 - INFO - ⏱️ 步驟5.2-收集Max/Min限制值 執行時間: 29.0ms
2025-07-23 17:40:31,706 - INFO - 🔄 步驟5.3：開始收集設備Bin信息...
2025-07-23 17:40:31,706 - INFO - 收集設備Bin信息...
2025-07-23 17:40:31,733 - INFO - 收集到7959個設備的Bin信息
2025-07-23 17:40:31,733 - INFO - ⏱️ 步驟5.3-收集設備Bin信息 執行時間: 27.0ms
2025-07-23 17:40:31,733 - INFO - 🔄 步驟5.4：開始分析設備測試結果...
2025-07-23 17:40:31,733 - INFO - 分析設備測試結果...
2025-07-23 17:40:31,952 - INFO - 應用染色邏輯：標記了1442個失敗測試值為紅色
2025-07-23 17:40:31,953 - INFO - ⏱️ 步驟5.4-分析設備測試結果 執行時間: 220.0ms
2025-07-23 17:40:31,953 - INFO - 🔄 步驟5.5：開始統計Bin數據...
2025-07-23 17:40:31,953 - INFO - 統計完成: 總設備7959, Pass6630, Fail1329, 良率83.30%
2025-07-23 17:40:31,953 - INFO - ⏱️ 步驟5.5-統計Bin數據 執行時間: <1ms
2025-07-23 17:40:31,953 - INFO - 🔄 步驟5.6：開始寫回設備Bin值...
2025-07-23 17:40:31,959 - INFO - 更新了7959個設備的Bin值
2025-07-23 17:40:31,959 - INFO - ⏱️ 步驟5.6-寫回設備Bin值 執行時間: 6.0ms
2025-07-23 17:40:31,959 - INFO - 🔄 步驟5.7：開始創建Summary工作表...
2025-07-23 17:40:31,960 - INFO - 創建增強Summary工作表...
2025-07-23 17:40:31,981 - INFO - VBA 457-469行：填充基本統計 Total=7959, Pass=6630, Fail=1329, Yield=83.30%
2025-07-23 17:40:31,982 - INFO - 已在Summary C1添加原始檔案連結: G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd
2025-07-23 17:40:32,014 - INFO - VBA 362-420行（修正版）：填充了17個測試項目的Bin數據，無重複
2025-07-23 17:40:32,014 - INFO - 計算實際第4列(Definition列)項目數量: 17
2025-07-23 17:40:32,014 - INFO - Site信息檢查: total_site_no=2, good_site_n=True, site_data數量=7959
2025-07-23 17:40:32,015 - INFO - 創建Site統計完成: 2個Site
2025-07-23 17:40:32,015 - INFO -   Site 1: 3758個設備
2025-07-23 17:40:32,015 - INFO -   Site 2: 4201個設備
2025-07-23 17:40:32,015 - INFO - 開始填充Site統計: 2個Site, 實際項目數量: 17
2025-07-23 17:40:32,015 - INFO - 填充Site 1統計完成: 3758個設備
2025-07-23 17:40:32,015 - INFO - 填充Site 2統計完成: 4201個設備
2025-07-23 17:40:32,015 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 17
2025-07-23 17:40:32,015 - INFO - 設置 AutoFilter 範圍: A6:I23
2025-07-23 17:40:32,016 - INFO - 按 B 欄由大到小排序了 17 行資料
2025-07-23 17:40:32,016 - INFO - 自動調整D列寬度: 16.4 (基於最大內容長度: 12)
2025-07-23 17:40:32,016 - INFO - 增強Summary工作表創建完成
2025-07-23 17:40:32,016 - INFO - ⏱️ 步驟5.7-創建Summary工作表 執行時間: 57.0ms
2025-07-23 17:40:32,016 - INFO - ✅ 步驟5：VBA核心分析完成
2025-07-23 17:40:32,016 - INFO - ⏱️ 步驟5-VBA核心分析 執行時間: 411.1ms
2025-07-23 17:40:32,016 - INFO - 🔄 步驟6：開始字體顏色設置...
2025-07-23 17:40:33,001 - INFO - ⚡ 已處理91000個cell的字體顏色
2025-07-23 17:40:33,042 - INFO - ⚡ 已處理96000個cell的字體顏色
2025-07-23 17:40:36,121 - INFO - ⚡ 已處理421000個cell的字體顏色
2025-07-23 17:40:36,967 - INFO - ⚡ 已處理515000個cell的字體顏色
2025-07-23 17:40:37,631 - INFO - ⚡ 已處理581000個cell的字體顏色
2025-07-23 17:40:40,395 - INFO - ⚡ 超級批量字體設置完成，總共處理866089個cell
2025-07-23 17:40:40,395 - INFO - 設置數據區域字體顏色（保留紅色）: 行13-7971, 列2-110
2025-07-23 17:40:40,395 - INFO - ⚡ 超級字體顏色優化完成，耗時8377.8ms
2025-07-23 17:40:40,395 - INFO - ⏱️ 步驟6-字體顏色設置 執行時間: 8.38s
2025-07-23 17:40:40,395 - INFO - ⏱️ 第三階段Device2BinControl處理總時間 執行時間: 8.98s
2025-07-23 17:40:40,395 - INFO - ✅ 第三階段：Device2BinControl處理完成
2025-07-23 17:41:09,016 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-23 17:41:15,395 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\dist\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300_spd_converted.xlsx，耗時6378.8ms
2025-07-23 17:41:15,395 - INFO - SPD直接轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\dist\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300_spd_converted.xlsx
2025-07-23 17:41:19,136 - INFO - 清理臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_xthesx9t
2025-07-23 17:41:31,298 - INFO - 開始完整轉換...
2025-07-23 17:41:31,298 - INFO - 檢測到SPD檔案，直接轉換為Excel...
2025-07-23 17:41:31,298 - INFO - 讀取CSV文件: C:/Users/<USER>/Desktop/test/G5440WC(CC)_GHBR04.1_02.spd
2025-07-23 17:41:31,334 - INFO - 成功讀取CSV文件，大小: (911, 110)
2025-07-23 17:41:31,334 - INFO - SPD檔案直接轉換，已是Data11格式
2025-07-23 17:41:31,479 - INFO - ⚡ 優化寫入完成: 911行 × 110列，耗時145.4ms
2025-07-23 17:41:31,479 - INFO - SPD檔案：在9A位置寫入TMT...
2025-07-23 17:41:31,479 - INFO - 應用FillEmptyItemName處理...
2025-07-23 17:41:31,479 - INFO - 🚀 第二階段：開始FillEmptyItemName處理...
2025-07-23 17:41:31,479 - INFO - 🔄 步驟1：開始計算總測試項目數量...
2025-07-23 17:41:31,483 - INFO - ⏱️ 步驟1-計算總測試項目數量 執行時間: 4.0ms
2025-07-23 17:41:31,483 - INFO - ✅ 步驟1：總測試項目數量: 298
2025-07-23 17:41:31,483 - INFO - 🔄 步驟2：開始填充測試項目名稱和編號...
2025-07-23 17:41:31,484 - INFO - ⏱️ 步驟2-填充測試項目名稱和編號 執行時間: <1ms
2025-07-23 17:41:31,484 - INFO - 🔄 步驟3：開始填充Min/Max值...
2025-07-23 17:41:31,484 - INFO - ⏱️ 步驟3-填充Min/Max值 執行時間: <1ms
2025-07-23 17:41:31,484 - INFO - ⏭️ 步驟4：跳過設備行清理（SPD檔案優化）
2025-07-23 17:41:31,484 - INFO - ⏱️ 第二階段FillEmptyItemName處理總時間 執行時間: 5.0ms
2025-07-23 17:41:31,484 - INFO - ✅ 第二階段：FillEmptyItemName處理完成
2025-07-23 17:41:31,484 - INFO - 應用Device2BinControl處理...
2025-07-23 17:41:31,484 - INFO - 🚀 第三階段：開始Device2BinControl處理...
2025-07-23 17:41:31,484 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-23 17:41:31,484 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-23 17:41:31,484 - INFO - 🔄 步驟2：開始測試儀類型檢測...
2025-07-23 17:41:31,484 - INFO - ⏱️ 步驟2-測試儀類型檢測 執行時間: <1ms
2025-07-23 17:41:31,484 - INFO - ✅ 步驟2：檢測到測試儀類型: TMT
2025-07-23 17:41:31,484 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-23 17:41:31,493 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-23 17:41:31,493 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 9.0ms
2025-07-23 17:41:31,493 - INFO - 🔄 步驟4：開始設備數據處理...
2025-07-23 17:41:31,498 - INFO - 設備總數: 899, 測試項目總數: 108
2025-07-23 17:41:31,498 - INFO - 設置了108個項目編號
2025-07-23 17:41:31,501 - INFO - 統一Site查找：在第8行第4列找到Site列，標題: Site
2025-07-23 17:41:31,504 - INFO - 統一Site收集完成: 總Site數=2, 設備數=899, 有效=True
2025-07-23 17:41:31,504 - INFO - 找到Site列在第4列
2025-07-23 17:41:31,504 - INFO - ⏱️ 步驟4-設備數據處理 執行時間: 11.0ms
2025-07-23 17:41:31,504 - INFO - 🔄 步驟5：開始VBA核心分析...
2025-07-23 17:41:31,504 - INFO - 開始VBA核心分析...
2025-07-23 17:41:31,506 - INFO - 🔄 步驟5.1：開始收集Site信息...
2025-07-23 17:41:31,509 - INFO - 統一Site查找：在第8行第4列找到Site列，標題: Site
2025-07-23 17:41:31,511 - INFO - 統一Site收集完成: 總Site數=2, 設備數=899, 有效=True
2025-07-23 17:41:31,511 - INFO - 收集Site信息完成: 總Site數=2, 設備數=899
2025-07-23 17:41:31,511 - INFO - ⏱️ 步驟5.1-收集Site信息 執行時間: 5.0ms
2025-07-23 17:41:31,511 - INFO - 🔄 步驟5.2：開始收集Max/Min限制值...
2025-07-23 17:41:31,511 - INFO - 收集Max/Min限制值...
2025-07-23 17:41:31,515 - INFO - 收集到Max限制值: 57個, Min限制值: 56個
2025-07-23 17:41:31,515 - INFO - ⏱️ 步驟5.2-收集Max/Min限制值 執行時間: 4.0ms
2025-07-23 17:41:31,515 - INFO - 🔄 步驟5.3：開始收集設備Bin信息...
2025-07-23 17:41:31,515 - INFO - 收集設備Bin信息...
2025-07-23 17:41:31,517 - INFO - 收集到899個設備的Bin信息
2025-07-23 17:41:31,517 - INFO - ⏱️ 步驟5.3-收集設備Bin信息 執行時間: 2.0ms
2025-07-23 17:41:31,517 - INFO - 🔄 步驟5.4：開始分析設備測試結果...
2025-07-23 17:41:31,517 - INFO - 分析設備測試結果...
2025-07-23 17:41:31,530 - INFO - 應用染色邏輯：標記了103個失敗測試值為紅色
2025-07-23 17:41:31,530 - INFO - ⏱️ 步驟5.4-分析設備測試結果 執行時間: 13.0ms
2025-07-23 17:41:31,530 - INFO - 🔄 步驟5.5：開始統計Bin數據...
2025-07-23 17:41:31,530 - INFO - 統計完成: 總設備899, Pass827, Fail72, 良率91.99%
2025-07-23 17:41:31,530 - INFO - ⏱️ 步驟5.5-統計Bin數據 執行時間: <1ms
2025-07-23 17:41:31,530 - INFO - 🔄 步驟5.6：開始寫回設備Bin值...
2025-07-23 17:41:31,531 - INFO - 更新了899個設備的Bin值
2025-07-23 17:41:31,531 - INFO - ⏱️ 步驟5.6-寫回設備Bin值 執行時間: 1.0ms
2025-07-23 17:41:31,531 - INFO - 🔄 步驟5.7：開始創建Summary工作表...
2025-07-23 17:41:31,531 - INFO - 創建增強Summary工作表...
2025-07-23 17:41:31,533 - INFO - VBA 457-469行：填充基本統計 Total=899, Pass=827, Fail=72, Yield=91.99%
2025-07-23 17:41:31,534 - INFO - 已在Summary C1添加原始檔案連結: G5440WC(CC)_GHBR04.1_02.spd
2025-07-23 17:41:31,538 - INFO - VBA 362-420行（修正版）：填充了6個測試項目的Bin數據，無重複
2025-07-23 17:41:31,538 - INFO - 計算實際第4列(Definition列)項目數量: 6
2025-07-23 17:41:31,538 - INFO - Site信息檢查: total_site_no=2, good_site_n=True, site_data數量=899
2025-07-23 17:41:31,538 - INFO - 創建Site統計完成: 2個Site
2025-07-23 17:41:31,538 - INFO -   Site 1: 439個設備
2025-07-23 17:41:31,538 - INFO -   Site 2: 460個設備
2025-07-23 17:41:31,538 - INFO - 開始填充Site統計: 2個Site, 實際項目數量: 6
2025-07-23 17:41:31,538 - INFO - 填充Site 1統計完成: 439個設備
2025-07-23 17:41:31,538 - INFO - 填充Site 2統計完成: 460個設備
2025-07-23 17:41:31,538 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 6
2025-07-23 17:41:31,538 - INFO - 設置 AutoFilter 範圍: A6:I12
2025-07-23 17:41:31,538 - INFO - 按 B 欄由大到小排序了 6 行資料
2025-07-23 17:41:31,538 - INFO - 自動調整D列寬度: 16.4 (基於最大內容長度: 12)
2025-07-23 17:41:31,538 - INFO - 增強Summary工作表創建完成
2025-07-23 17:41:31,538 - INFO - ⏱️ 步驟5.7-創建Summary工作表 執行時間: 7.0ms
2025-07-23 17:41:31,538 - INFO - ✅ 步驟5：VBA核心分析完成
2025-07-23 17:41:31,538 - INFO - ⏱️ 步驟5-VBA核心分析 執行時間: 34.0ms
2025-07-23 17:41:31,538 - INFO - 🔄 步驟6：開始字體顏色設置...
2025-07-23 17:41:32,374 - INFO - ⚡ 超級批量字體設置完成，總共處理97888個cell
2025-07-23 17:41:32,374 - INFO - 設置數據區域字體顏色（保留紅色）: 行13-911, 列2-110
2025-07-23 17:41:32,374 - INFO - ⚡ 超級字體顏色優化完成，耗時836.0ms
2025-07-23 17:41:32,374 - INFO - ⏱️ 步驟6-字體顏色設置 執行時間: 836.0ms
2025-07-23 17:41:32,374 - INFO - ⏱️ 第三階段Device2BinControl處理總時間 執行時間: 890.0ms
2025-07-23 17:41:32,374 - INFO - ✅ 第三階段：Device2BinControl處理完成
2025-07-23 17:41:34,908 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-23 17:41:36,089 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\dist\G5440WC(CC)_GHBR04.1_02_spd_converted.xlsx，耗時1182.0ms
2025-07-23 17:41:36,089 - INFO - SPD直接轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\dist\G5440WC(CC)_GHBR04.1_02_spd_converted.xlsx
2025-07-23 17:41:36,092 - INFO - 開始完整轉換...
2025-07-23 17:41:36,092 - INFO - 讀取CSV文件: C:/Users/<USER>/Desktop/test/GMT_G2304.csv
2025-07-23 17:41:36,106 - INFO - 成功讀取CSV文件，大小: (3511, 60)
2025-07-23 17:41:36,107 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-23 17:41:36,335 - INFO - ⚡ 優化寫入完成: 3484行 × 60列，耗時226.0ms
2025-07-23 17:41:36,338 - INFO - ⚡ 優化寫入完成: 22行 × 60列，耗時2.0ms
2025-07-23 17:41:36,339 - INFO - ⚡ 優化寫入完成: 4行 × 60列，耗時0.0ms
2025-07-23 17:41:36,339 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時231.0ms
2025-07-23 17:41:36,339 - INFO - 應用CTA8280格式...
2025-07-23 17:41:36,339 - INFO - 🚀 第一階段：開始CTA8280格式處理...
2025-07-23 17:41:36,339 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-23 17:41:36,339 - INFO - 找到標題行在第2行
2025-07-23 17:41:36,339 - INFO - ⏱️ 步驟1-找到標題行 執行時間: <1ms
2025-07-23 17:41:36,339 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-23 17:41:36,340 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: 1.0ms
2025-07-23 17:41:36,340 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-23 17:41:36,340 - INFO - myFindedRowN=2, myColumnN=14
2025-07-23 17:41:36,345 - INFO - ⚡ 超級批量插入6行完成，耗時1.0ms
2025-07-23 17:41:36,345 - INFO - 插入了6行在最前面
2025-07-23 17:41:36,345 - INFO - ⚡ 標題行插入優化完成，耗時1.0ms
2025-07-23 17:41:36,345 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 5.0ms
2025-07-23 17:41:36,345 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-23 17:41:36,345 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-23 17:41:36,345 - INFO - 清空位置 A8: "Index_No"
2025-07-23 17:41:36,345 - INFO - 清空位置 B8: "Dut_No"
2025-07-23 17:41:36,345 - INFO - 清空位置 A10: "ASD"
2025-07-23 17:41:36,345 - INFO - 清空位置 B10: "QQ"
2025-07-23 17:41:36,345 - INFO - 清空位置 A11: "A"
2025-07-23 17:41:36,345 - INFO - 清空位置 B11: "B"
2025-07-23 17:41:36,345 - INFO - ✅ 清空了6個多餘資料位置
2025-07-23 17:41:36,346 - INFO - 最後一列資料密度正常(31.0%)，保留資料
2025-07-23 17:41:36,346 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 1.0ms
2025-07-23 17:41:36,346 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-23 17:41:36,347 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: 1.0ms
2025-07-23 17:41:36,347 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-23 17:41:36,347 - INFO - 找到SW_Bin列在第6列
2025-07-23 17:41:36,347 - INFO - 處理了16個設備的數據
2025-07-23 17:41:36,347 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: <1ms
2025-07-23 17:41:36,347 - INFO - ⏱️ 第一階段CTA8280格式處理總時間 執行時間: 8.0ms
2025-07-23 17:41:36,347 - INFO - ✅ 第一階段：CTA8280格式處理完成
2025-07-23 17:41:36,347 - INFO - 應用FillEmptyItemName處理...
2025-07-23 17:41:36,347 - INFO - 🚀 第二階段：開始FillEmptyItemName處理...
2025-07-23 17:41:36,347 - INFO - 🔄 步驟1：開始計算總測試項目數量...
2025-07-23 17:41:36,347 - INFO - ⏱️ 步驟1-計算總測試項目數量 執行時間: <1ms
2025-07-23 17:41:36,347 - INFO - ✅ 步驟1：總測試項目數量: 58
2025-07-23 17:41:36,347 - INFO - 🔄 步驟2：開始填充測試項目名稱和編號...
2025-07-23 17:41:36,348 - INFO - ⏱️ 步驟2-填充測試項目名稱和編號 執行時間: <1ms
2025-07-23 17:41:36,348 - INFO - 🔄 步驟3：開始填充Min/Max值...
2025-07-23 17:41:36,349 - INFO - ⏱️ 步驟3-填充Min/Max值 執行時間: 1.0ms
2025-07-23 17:41:36,349 - INFO - 🔄 步驟4：開始清理設備行...
2025-07-23 17:41:36,349 - INFO - 總設備數量: 16
2025-07-23 17:41:36,349 - INFO - ⏱️ 步驟4-清理設備行 執行時間: <1ms
2025-07-23 17:41:36,349 - INFO - ⏱️ 第二階段FillEmptyItemName處理總時間 執行時間: 2.0ms
2025-07-23 17:41:36,349 - INFO - ✅ 第二階段：FillEmptyItemName處理完成
2025-07-23 17:41:36,349 - INFO - 應用Device2BinControl處理...
2025-07-23 17:41:36,349 - INFO - 🚀 第三階段：開始Device2BinControl處理...
2025-07-23 17:41:36,349 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-23 17:41:36,349 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-23 17:41:36,349 - INFO - 🔄 步驟2：開始測試儀類型檢測...
2025-07-23 17:41:36,349 - INFO - ⏱️ 步驟2-測試儀類型檢測 執行時間: <1ms
2025-07-23 17:41:36,350 - INFO - ✅ 步驟2：檢測到測試儀類型: CTA
2025-07-23 17:41:36,350 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-23 17:41:36,350 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-23 17:41:36,350 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: <1ms
2025-07-23 17:41:36,350 - INFO - 🔄 步驟4：開始設備數據處理...
2025-07-23 17:41:36,350 - INFO - 設備總數: 16, 測試項目總數: 58
2025-07-23 17:41:36,350 - INFO - 設置了58個項目編號
2025-07-23 17:41:36,351 - INFO - 統一Site查找：在第8行第4列找到Site列，標題: Site_No
2025-07-23 17:41:36,351 - INFO - 統一Site收集完成: 總Site數=4, 設備數=16, 有效=False
2025-07-23 17:41:36,351 - INFO - 找到Site列在第4列
2025-07-23 17:41:36,351 - INFO - ⏱️ 步驟4-設備數據處理 執行時間: 1.0ms
2025-07-23 17:41:36,351 - INFO - 🔄 步驟5：開始VBA核心分析...
2025-07-23 17:41:36,351 - INFO - 開始VBA核心分析...
2025-07-23 17:41:36,351 - INFO - 🔄 步驟5.1：開始收集Site信息...
2025-07-23 17:41:36,351 - INFO - 統一Site查找：在第8行第4列找到Site列，標題: Site_No
2025-07-23 17:41:36,351 - INFO - 統一Site收集完成: 總Site數=4, 設備數=16, 有效=False
2025-07-23 17:41:36,351 - INFO - 收集Site信息完成: 總Site數=4, 設備數=16
2025-07-23 17:41:36,351 - INFO - ⏱️ 步驟5.1-收集Site信息 執行時間: <1ms
2025-07-23 17:41:36,351 - INFO - 🔄 步驟5.2：開始收集Max/Min限制值...
2025-07-23 17:41:36,351 - INFO - 收集Max/Min限制值...
2025-07-23 17:41:36,351 - INFO - 收集到Max限制值: 43個, Min限制值: 43個
2025-07-23 17:41:36,351 - INFO - ⏱️ 步驟5.2-收集Max/Min限制值 執行時間: <1ms
2025-07-23 17:41:36,351 - INFO - 🔄 步驟5.3：開始收集設備Bin信息...
2025-07-23 17:41:36,351 - INFO - 收集設備Bin信息...
2025-07-23 17:41:36,351 - INFO - 收集到16個設備的Bin信息
2025-07-23 17:41:36,351 - INFO - ⏱️ 步驟5.3-收集設備Bin信息 執行時間: <1ms
2025-07-23 17:41:36,352 - INFO - 🔄 步驟5.4：開始分析設備測試結果...
2025-07-23 17:41:36,352 - INFO - 分析設備測試結果...
2025-07-23 17:41:36,353 - INFO - 應用染色邏輯：標記了12個失敗測試值為紅色
2025-07-23 17:41:36,353 - INFO - ⏱️ 步驟5.4-分析設備測試結果 執行時間: <1ms
2025-07-23 17:41:36,353 - INFO - 🔄 步驟5.5：開始統計Bin數據...
2025-07-23 17:41:36,353 - INFO - 統計完成: 總設備16, Pass4, Fail12, 良率25.00%
2025-07-23 17:41:36,353 - INFO - ⏱️ 步驟5.5-統計Bin數據 執行時間: <1ms
2025-07-23 17:41:36,353 - INFO - 🔄 步驟5.6：開始寫回設備Bin值...
2025-07-23 17:41:36,353 - INFO - 更新了16個設備的Bin值
2025-07-23 17:41:36,353 - INFO - ⏱️ 步驟5.6-寫回設備Bin值 執行時間: <1ms
2025-07-23 17:41:36,353 - INFO - 🔄 步驟5.7：開始創建Summary工作表...
2025-07-23 17:41:36,353 - INFO - 創建增強Summary工作表...
2025-07-23 17:41:36,353 - INFO - VBA 457-469行：填充基本統計 Total=16, Pass=4, Fail=12, Yield=25.00%
2025-07-23 17:41:36,353 - INFO - 已在Summary C1添加原始檔案連結: GMT_G2304.csv
2025-07-23 17:41:36,353 - INFO - VBA 362-420行（修正版）：填充了2個測試項目的Bin數據，無重複
2025-07-23 17:41:36,353 - INFO - 計算實際第4列(Definition列)項目數量: 2
2025-07-23 17:41:36,353 - INFO - Site信息檢查: total_site_no=4, good_site_n=False, site_data數量=16
2025-07-23 17:41:36,354 - INFO - 沒有有效的Site數據，跳過Site統計。原因: total_site_no=4, good_site_n=False
2025-07-23 17:41:36,354 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 2
2025-07-23 17:41:36,354 - INFO - 設置 AutoFilter 範圍: A6:E8
2025-07-23 17:41:36,354 - INFO - 按 B 欄由大到小排序了 2 行資料
2025-07-23 17:41:36,354 - INFO - 自動調整D列寬度: 14.0 (基於最大內容長度: 10)
2025-07-23 17:41:36,354 - INFO - 增強Summary工作表創建完成
2025-07-23 17:41:36,354 - INFO - ⏱️ 步驟5.7-創建Summary工作表 執行時間: 1.0ms
2025-07-23 17:41:36,354 - INFO - ✅ 步驟5：VBA核心分析完成
2025-07-23 17:41:36,354 - INFO - ⏱️ 步驟5-VBA核心分析 執行時間: 3.0ms
2025-07-23 17:41:36,354 - INFO - 🔄 步驟6：開始字體顏色設置...
2025-07-23 17:41:36,364 - INFO - ⚡ 超級批量字體設置完成，總共處理932個cell
2025-07-23 17:41:36,364 - INFO - 設置數據區域字體顏色（保留紅色）: 行13-28, 列2-60
2025-07-23 17:41:36,364 - INFO - ⚡ 超級字體顏色優化完成，耗時10.0ms
2025-07-23 17:41:36,364 - INFO - ⏱️ 步驟6-字體顏色設置 執行時間: 10.0ms
2025-07-23 17:41:36,365 - INFO - ⏱️ 第三階段Device2BinControl處理總時間 執行時間: 16.0ms
2025-07-23 17:41:36,365 - INFO - ✅ 第三階段：Device2BinControl處理完成
2025-07-23 17:41:37,332 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-23 17:41:37,644 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\dist\GMT_G2304_clean_converted.xlsx，耗時311.9ms
2025-07-23 17:41:37,644 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\dist\GMT_G2304_clean_converted.xlsx
