@echo off
echo 安裝PyInstaller和相關套件...

echo 1. 升級pip...
python -m pip install --upgrade pip

echo.
echo 2. 安裝PyInstaller...
pip install pyinstaller

echo.
echo 3. 檢查安裝結果...
pyinstaller --version
if %errorlevel% neq 0 (
    echo ❌ PyInstaller安裝失敗
    echo 嘗試使用python -m方式...
    python -m pip install pyinstaller
) else (
    echo ✅ PyInstaller安裝成功
)

echo.
echo 4. 安裝必要套件（如果缺少）...
pip install pandas openpyxl

echo.
echo 安裝完成！現在可以使用PyInstaller了
pause
