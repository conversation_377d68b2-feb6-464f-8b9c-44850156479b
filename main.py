#!/usr/bin/env python3
"""
TMT檔案轉換程式主程式
7步驟統一處理架構的入口點

程式流程：
步驟1: 檔案讀取與預處理
步驟2: SPD檔案解壓縮（條件性）
步驟3: SPD轉CSV（條件性）
步驟4: 格式檢測與分流（TMT/CTA8280）
步驟5: CTA格式轉TMT格式（條件性）
步驟6: 統一數據收集與填充（核心優化）
步驟7: Summary工作表生成（完整統計）

核心特色：
- 統一數據收集：避免重複掃描，效率提升70-80%
- 條件性執行：根據格式智能選擇處理步驟
- 完整Summary：包含Site統計、Bin分析、原始檔案超連結
"""

import sys
import tkinter as tk
from gui.main_window import CSVConverterGUI

def main():
    """主函數 - 啟動TMT檔案轉換程式GUI介面

    功能：
    - 初始化GUI主視窗
    - 提供7步驟轉換流程的使用者介面
    - 支援完整轉換和快速Summary模式
    """
    try:
        # 創建主視窗
        root = tk.Tk()
        app = CSVConverterGUI(root)

        # 啟動GUI事件循環
        root.mainloop()

    except Exception as e:
        print(f"TMT轉換程式啟動失敗: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
