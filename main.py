#!/usr/bin/env python3
"""
CSV轉換器主程式
模組化架構的入口點
"""

import sys
import tkinter as tk
from gui.main_window import CSVConverterGUI

def main():
    """主函數"""
    try:
        # 創建主視窗
        root = tk.Tk()
        app = CSVConverterGUI(root)
        
        # 啟動GUI
        root.mainloop()
        
    except Exception as e:
        print(f"程式啟動失敗: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
