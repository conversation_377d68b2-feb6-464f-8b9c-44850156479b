# -*- mode: python ; coding: utf-8 -*-
# 優化版本的PyInstaller配置檔案

# 排除不必要的模組
excludes = [
    # 機器學習和AI相關
    'torch', 'tensorflow', 'keras', 'sklearn', 'scipy',
    'numpy.distutils', 'numpy.f2py', 'numpy.testing',
    
    # 網路和API相關
    'fastapi', 'starlette', 'uvicorn', 'httpx', 'aiohttp',
    'requests_oauthlib', 'urllib3_secure_extra',
    
    # 科學計算相關
    'astropy', 'dask', 'distributed', 'joblib',
    'matplotlib', 'seaborn', 'plotly',
    
    # 開發和測試工具
    'pytest', 'unittest', 'doctest', 'pdb', 'profile',
    'cProfile', 'pstats', 'trace', 'tracemalloc',
    
    # 文檔和格式化
    'sphinx', 'docutils', 'markdown', 'jinja2',
    
    # 其他不必要的模組
    'IPython', 'jupyter', 'notebook', 'ipywidgets',
    'babel', 'pytz.zoneinfo', 'email.mime',
    
    # 壓縮相關（如果不需要）
    'lzma', 'bz2', 'gzip', 'zipfile', 'tarfile',
    
    # 加密相關（如果不需要）
    'cryptography', 'ssl', 'hashlib',
    
    # 多媒體相關
    'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont',
]

# 隱藏導入（只包含必要的）
hiddenimports = [
    'openpyxl.cell._writer',
    'openpyxl.worksheet._writer',
    'pandas._libs.tslibs.timedeltas',
    'pandas._libs.tslibs.np_datetime',
    'pandas._libs.tslibs.nattype',
    'pandas._libs.tslibs.timestamps',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=2,  # 最高優化等級
)

# 移除不必要的二進制檔案
def remove_from_list(input_list, item_name):
    """從列表中移除包含特定名稱的項目"""
    return [x for x in input_list if item_name not in x[0].lower()]

# 移除不必要的二進制檔案
for exclude_name in ['torch', 'tensorflow', 'sklearn', 'scipy', 'matplotlib']:
    a.binaries = remove_from_list(a.binaries, exclude_name)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='CSV轉換器_優化版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,  # 啟用strip
    upx=True,   # 啟用UPX壓縮
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    uac_admin=False,  # 不需要管理員權限
    icon=None,  # 如果有圖示檔案可以加上
)
