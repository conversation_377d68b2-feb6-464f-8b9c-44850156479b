# -*- mode: python ; coding: utf-8 -*-
# 超級精簡版PyInstaller配置

excludes = [
    'torch', 'tensorflow', 'keras', 'sklearn', 'scipy', 'matplotlib', 
    'seaborn', 'plotly', 'fastapi', 'starlette', 'uvicorn', 'httpx', 
    'aiohttp', 'astropy', 'dask', 'distributed', 'joblib', 'pytest', 
    'unittest', 'doctest', 'sphinx', 'docutils', 'jinja2', 'IPython', 
    'jupyter', 'notebook', 'babel', 'cryptography', 'PIL.ImageTk',
    'numpy.distutils', 'numpy.f2py', 'numpy.testing', 'pandas.plotting',
    'openpyxl.drawing', 'openpyxl.chart'
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=2,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='CSV轉換器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
