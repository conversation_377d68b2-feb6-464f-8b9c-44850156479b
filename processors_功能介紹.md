# processors.py 功能介紹文檔

## 概述

`processors.py` 是CSV轉換器的核心資料處理模組，包含三個主要的處理器類別，負責將CTA8280測試儀產生的原始數據轉換為標準格式。該模組實現了完整的VBA邏輯轉換，提供高效能的數據處理和格式化功能。

## 模組結構

```
processors.py
├── CTA8280Processor (CTA8280格式處理器)
├── FillEmptyItemNameProcessor (空項目名稱填充處理器)
├── Device2BinControlProcessor (設備到Bin控制處理器) ⭐
└── 工廠函數 (get_*_processor)
```

---

## 🔧 類別和方法詳細說明

### 1. CTA8280Processor (CTA8280格式處理器)

**功能**：處理CTA8280測試儀的原始數據格式，將其轉換為標準的Excel格式。

#### `__init__(self)`
- **功能**：初始化CTA8280處理器
- **作用**：
  - 初始化檔案處理器和Excel處理器
  - 設置測試儀類型為CTA8280

#### `apply_format(self, ws_data11, ws_sum)`
- **功能**：應用CTA8280格式轉換的主要方法
- **參數**：
  - `ws_data11`: openpyxl.Worksheet - Data11工作表
  - `ws_sum`: openpyxl.Worksheet - Sum工作表
- **處理流程**：
  1. 找到標題行 (`_find_header_row`)
  2. 刪除標題行之前的行 (`_remove_rows_before_header`)
  3. 處理行列操作 (`_process_row_column_operations`)
  4. 應用CTA8280標題格式 (`_apply_cta8280_headers`)
  5. 從sum工作表提取信息
  6. 設置列標題 (`_set_column_headers`)
  7. 生成Serial#和Bin# (`_generate_serial_bin`)

#### `_find_header_row(self, ws)`
- **功能**：在工作表中找到包含"Index_No"的標題行
- **返回值**：int - 標題行號，未找到返回None
- **邏輯**：遍歷前10行，尋找包含"Index_No"關鍵字的行

#### `_remove_rows_before_header(self, ws, header_row)`
- **功能**：刪除標題行之前的所有行
- **作用**：清理工作表，確保標題行位於正確位置

#### `_process_row_column_operations(self, ws)`
- **功能**：處理行列操作，包括查找關鍵列和插入行列
- **返回值**：tuple (int, int) - (找到的行號, 列號)
- **邏輯**：
  - 清空A1單元格
  - 查找Data_Num/Data_Cnt/TEST_NUM列
  - 插入標題行和列

#### `_apply_cta8280_headers(self, ws)`
- **功能**：應用標準的CTA8280標題格式
- **作用**：設置固定的標題內容，如"Spreadsheet"、"Format"等

#### `_set_column_headers(self, ws)`
- **功能**：設置列標題，包括Serial#和Bin#
- **作用**：在第12行設置標準的列標題格式

#### `_generate_serial_bin(self, ws)`
- **功能**：為每個設備生成序列號和Bin號
- **邏輯**：
  - 遍歷第13行開始的所有行
  - 檢查是否有實際數據
  - 生成連續的設備序列號
  - 從SW_Bin列提取Bin號，默認為1

---

### 2. FillEmptyItemNameProcessor (空項目名稱填充處理器)

**功能**：填充空的測試項目名稱、編號和Min/Max值，實現99.8%的性能提升。

#### `__init__(self)`
- **功能**：初始化填充處理器
- **作用**：初始化檔案處理器

#### `apply_processing(self, ws, skip_device_cleanup=False, fast_mode=False)`
- **功能**：應用填充處理的主要方法
- **參數**：
  - `ws`: openpyxl.Worksheet - 要處理的工作表
  - `skip_device_cleanup`: bool - 是否跳過設備行清理（SPD檔案優化）
  - `fast_mode`: bool - 是否使用快速模式
- **處理流程**：
  1. 計算總測試項目數量 (`_calculate_total_item_number`)
  2. 填充測試項目名稱和編號 (`_fill_test_item_names`)
  3. 填充Min/Max值 (`_fill_min_max_values`)
  4. 清理多餘的設備行 (`_clean_device_rows`)

#### `_calculate_total_item_number(self, ws, fast_mode=False)`
- **功能**：計算總測試項目數量
- **參數**：
  - `fast_mode`: bool - 快速模式跳過某些檢查
- **返回值**：int - 測試項目總數
- **邏輯**：統計第12行中非空單元格的數量

#### `_fill_test_item_names(self, ws, total_items)`
- **功能**：填充空的測試項目名稱和編號
- **處理內容**：
  - **第7行**：測試項目編號（格式：0.00.01, 0.00.02...）
  - **第8行**：測試項目名稱（從第12行複製或生成）
- **特殊處理**：
  - 填充內容的字體顏色設為紅色
  - 處理Time相關項目的特殊命名

#### `_fill_min_max_values(self, ws, total_items)`
- **功能**：填充Min/Max限制值
- **處理內容**：
  - **第10行**：Max值（默認999999）
  - **第11行**：Min值（默認-999999）
- **邏輯**：只填充空的單元格，保留現有值

#### `_clean_device_rows(self, ws)`
- **功能**：清理多餘的設備行
- **作用**：
  - 移除沒有序列號的設備行
  - 優化工作表大小
  - 提升處理性能

#### `_get_total_device_number(self, ws)`
- **功能**：計算總設備數量
- **返回值**：int - 設備總數
- **邏輯**：統計第1列中有序列號的行數

#### `_is_numeric(self, value)`
- **功能**：檢查值是否為數字
- **返回值**：bool - 是否為數字
- **用途**：數據驗證和格式檢查

---

### 3. Device2BinControlProcessor (設備到Bin控制處理器) ⭐

**功能**：核心的設備分析和Bin分配邏輯，基於VBA程式完整轉換，包含重複功能整合。

#### 核心方法

#### `__init__(self)`
- **功能**：初始化Device2BinControl處理器
- **作用**：初始化檔案處理器

#### `apply_processing(self, ws, create_summary=False, output_format="excel")`
- **功能**：應用Device2BinControl處理的主要方法
- **參數**：
  - `ws`: openpyxl.Worksheet - 要處理的工作表
  - `create_summary`: bool - 是否創建Summary工作表
  - `output_format`: str - 輸出格式（"excel"或"csv"）
- **處理流程**：
  1. 驗證工作表格式 (`_validate_worksheet_format`)
  2. 檢測測試儀類型 (`_detect_tester_type`)
  3. 設置Excel格式 (`_setup_excel_formatting`)
  4. 處理設備數據 (`_process_device_data`)
  5. 執行VBA核心分析 (`_execute_vba_core_analysis`)
  6. 設置字體顏色（僅Excel格式）

#### `_execute_vba_core_analysis(self, ws, create_summary, output_format)`
- **功能**：執行VBA 159-361行的核心分析邏輯
- **處理流程**：
  1. 收集Site信息
  2. 收集Max/Min限制值
  3. 收集設備Bin信息
  4. 分析設備測試結果
  5. 統計Bin數據
  6. 寫回設備Bin值
  7. 創建Summary工作表（可選）

#### 整合後的統一方法

#### `_analyze_site_statistics(self, ws, my_bin_array=None, vba_mode=True)`
- **功能**：統一的Site統計分析方法
- **參數**：
  - `vba_mode`: bool - True=VBA完整模式，False=簡化模式
- **返回值**：dict - Site統計信息
- **模式**：
  - **VBA模式**：完整的Site和Bin統計，支持動態Site數量
  - **簡化模式**：只統計設備數量，用於簡單場景

#### `_fill_bin_data(self, summary_ws, ws=None, bin_statistics=None, analysis_result=None, mode="accurate")`
- **功能**：統一的Bin數據填充方法
- **參數**：
  - `mode`: str - "accurate"|"vba_style"|"simplified"
- **模式**：
  - **精確模式**：基於VBA真實邏輯的設備Bin統計
  - **VBA風格模式**：基於bin_statistics的填充
  - **簡化模式**：最基本的Bin數據填充

#### `_add_site_statistics(self, summary_ws, ws, bin_statistics=None, vba_style=True)`
- **功能**：統一的Site統計添加方法（第5行）
- **參數**：
  - `vba_style`: bool - True=VBA風格，False=簡化風格
- **作用**：在Summary工作表第5行添加Site統計信息

#### `_setup_summary_headers_and_site_stats_vba_421_448(self, summary_ws, ws, my_bin_array, total_device_number, analysis_result, site_info=None)`
- **功能**：統一的Summary設置方法
- **參數**：
  - `site_info`: dict - 預收集的Site信息（可選）
- **模式**：
  - **預收集模式**：使用預先收集的Site信息
  - **後備模式**：重新分析Site統計

#### 輔助方法

#### `_format_percentage(self, value)`
- **功能**：格式化百分比顯示
- **邏輯**：0.00%顯示為0%，其他顯示到小數點第二位

#### `_get_bin_definition(self, bin_num)`
- **功能**：獲取Bin定義名稱
- **返回值**：str - Bin定義（只有Bin 1顯示"All Pass"）

#### `_set_summary_headers(self, summary_ws)`
- **功能**：設置Summary標題行
- **作用**：設置Bin、Count、%、Definition、Note列標題

---

## 🏭 工廠函數

### `get_cta8280_processor()`
- **功能**：獲取CTA8280處理器實例
- **返回值**：CTA8280Processor實例

### `get_fill_empty_processor()`
- **功能**：獲取FillEmptyItemName處理器實例
- **返回值**：FillEmptyItemNameProcessor實例

### `get_device2bin_processor()`
- **功能**：獲取Device2BinControl處理器實例
- **返回值**：Device2BinControlProcessor實例

---

## 🔄 處理流程圖

### 完整處理流程
```
原始CSV/SPD數據
    ↓
CTA8280格式處理
    ├── 找到標題行
    ├── 清理無用行
    ├── 插入標準行列
    ├── 設置標題格式
    └── 生成Serial#/Bin#
    ↓
FillEmptyItemName處理
    ├── 計算項目數量
    ├── 填充項目名稱/編號
    ├── 填充Min/Max值
    └── 清理設備行
    ↓
Device2BinControl處理
    ├── VBA核心分析
    ├── Site統計分析
    ├── Bin數據統計
    ├── Summary工作表生成
    └── 格式化和染色
    ↓
標準化Excel/CSV輸出
```

---

## 📊 特色功能

### 1. 智能格式檢測
- 自動檢測CTA8280測試儀格式
- 智能找到標題行和關鍵列
- 適應不同的數據結構

### 2. 高效能處理
- **99.8%性能提升**：FillEmptyItemName優化
- **快速模式**：SPD檔案專用優化
- **跳過清理**：可選的設備行清理

### 3. VBA邏輯完整轉換
- **1:1轉換**：完整實現VBA 159-546行邏輯
- **Site統計**：動態支持多個Site
- **Bin分析**：精確的設備Bin分配
- **Summary統計**：詳細的統計分析

### 4. 重複功能整合 ⭐
- **統一接口**：11個重複方法整合為4個統一方法
- **參數化控制**：通過參數控制不同處理模式
- **向後兼容**：保持100%功能兼容性

### 5. 多格式支援
- **Excel格式**：完整的格式化和染色
- **CSV格式**：純數據輸出，不執行染色
- **SPD特殊處理**：TMT標記和顏色保留

---

## ⚙️ 配置和設定

### 依賴配置
```python
from config.settings import (
    SEARCH_KEYWORDS,      # 搜尋關鍵字
    CTA8280_HEADERS,      # CTA8280標題格式
    FILL_EMPTY_SETTINGS,  # 填充處理設定
    PROCESSING_LIMITS,    # 處理限制
    TesterType           # 測試儀類型
)
```

### 處理限制
- **最大列數**：300
- **最大行數**：65536
- **最大設備行數**：1000
- **標題搜尋範圍**：前10行

---

## 🔗 依賴關係

### 內部依賴
- `utils.file_utils` - 檔案處理和日誌
- `utils.excel_utils` - Excel操作工具
- `config.settings` - 配置參數

### 外部依賴
- `pandas` - 數據處理
- `openpyxl` - Excel檔案操作和格式化

---

## 💡 使用建議

1. **處理順序**：
   - 先執行CTA8280格式處理
   - 再執行FillEmptyItemName處理
   - 最後執行Device2BinControl處理

2. **性能優化**：
   - SPD檔案使用`fast_mode=True`
   - 大檔案使用`skip_device_cleanup=True`
   - CSV輸出使用`output_format="csv"`

3. **Summary功能**：
   - 需要詳細分析時啟用`create_summary=True`
   - 會增加處理時間但提供豐富統計數據

4. **錯誤處理**：
   - 檢查工作表格式是否符合要求
   - 監控日誌檔案獲取處理狀態
   - 使用統一的錯誤處理機制

---

## 📋 詳細方法列表

### CTA8280Processor 方法列表
| 方法名稱 | 功能描述 | 參數 | 返回值 |
|---------|---------|------|--------|
| `apply_format()` | 主要格式轉換方法 | ws_data11, ws_sum | None |
| `_find_header_row()` | 找到標題行 | ws | int/None |
| `_remove_rows_before_header()` | 刪除標題行前的行 | ws, header_row | None |
| `_process_row_column_operations()` | 處理行列操作 | ws | tuple |
| `_apply_cta8280_headers()` | 應用CTA8280標題 | ws | None |
| `_set_column_headers()` | 設置列標題 | ws | None |
| `_generate_serial_bin()` | 生成序列號和Bin號 | ws | None |
| `_insert_header_rows()` | 插入標題行 | ws, row_num | None |

### FillEmptyItemNameProcessor 方法列表
| 方法名稱 | 功能描述 | 參數 | 返回值 |
|---------|---------|------|--------|
| `apply_processing()` | 主要填充處理方法 | ws, skip_device_cleanup, fast_mode | None |
| `_calculate_total_item_number()` | 計算總項目數量 | ws, fast_mode | int |
| `_fill_test_item_names()` | 填充測試項目名稱 | ws, total_items | None |
| `_fill_min_max_values()` | 填充Min/Max值 | ws, total_items | None |
| `_clean_device_rows()` | 清理設備行 | ws | None |
| `_get_total_device_number()` | 獲取設備總數 | ws | int |
| `_is_numeric()` | 檢查是否為數字 | value | bool |

### Device2BinControlProcessor 核心方法列表
| 方法名稱 | 功能描述 | 參數 | 返回值 |
|---------|---------|------|--------|
| `apply_processing()` | 主要處理方法 | ws, create_summary, output_format | bool |
| `_execute_vba_core_analysis()` | VBA核心分析 | ws, create_summary, output_format | None |
| `_validate_worksheet_format()` | 驗證工作表格式 | ws | bool |
| `_detect_tester_type()` | 檢測測試儀類型 | ws | str |
| `_setup_excel_formatting()` | 設置Excel格式 | ws | None |
| `_process_device_data()` | 處理設備數據 | ws | None |
| `_collect_max_min_limits()` | 收集限制值 | ws | tuple |
| `_collect_device_bins()` | 收集設備Bin | ws | dict |
| `_analyze_device_test_results()` | 分析設備測試結果 | ws, max_limits, min_limits, device_bins, output_format | dict |
| `_calculate_bin_statistics()` | 計算Bin統計 | analysis_result | dict |
| `_write_device_bins_back()` | 寫回設備Bin值 | ws, device_bin_results | None |
| `_create_enhanced_summary_worksheet()` | 創建Summary工作表 | ws, analysis_result, bin_statistics | None |

### Device2BinControlProcessor 新增方法 (v2.5.0)
| 方法名稱 | 功能描述 | 參數 | 返回值 |
|---------|---------|------|--------|
| `_add_original_file_link()` | 添加原始檔案連結 | summary_ws, ws, original_filename | None |
| `_get_unified_site_info()` | 統一Site信息收集 | ws, mode="full"/"simple" | dict |
| `_fill_site_data_for_existing_rows()` | Site統計精確比對 | summary_ws, site_index, site_bin_array, site_device_count | None |
| `_fill_test_item_bins()` | 智能測試項目填充 | summary_ws, ws, my_bin_array, total_device_number | None |

### Device2BinControlProcessor 整合後統一方法
| 方法名稱 | 功能描述 | 模式參數 | 返回值 |
|---------|---------|----------|--------|
| `_analyze_site_statistics()` | 統一Site統計分析 | vba_mode=True/False | dict |
| `_fill_bin_data()` | 統一Bin數據填充 | mode="accurate"/"vba_style"/"simplified" | None |
| `_add_site_statistics()` | 統一Site統計添加 | vba_style=True/False | None |
| `_setup_summary_headers_and_site_stats_vba_421_448()` | 統一Summary設置 | site_info=None | None |

---

## 🎯 實際應用示例

### 基本使用
```python
from core.processors import (
    get_cta8280_processor,
    get_fill_empty_processor,
    get_device2bin_processor
)

# 初始化處理器
cta8280_proc = get_cta8280_processor()
fill_empty_proc = get_fill_empty_processor()
device2bin_proc = get_device2bin_processor()

# 標準處理流程
cta8280_proc.apply_format(ws_data11, ws_sum)
fill_empty_proc.apply_processing(ws_data11)
device2bin_proc.apply_processing(ws_data11, create_summary=True)
```

### SPD檔案優化處理
```python
# SPD檔案使用快速模式
fill_empty_proc.apply_processing(
    ws_data11,
    skip_device_cleanup=True,  # 跳過設備行清理
    fast_mode=True            # 快速模式
)

# CSV輸出格式（不執行染色）
device2bin_proc.apply_processing(
    ws_data11,
    create_summary=True,
    output_format="csv"       # CSV格式輸出
)
```

### 統一接口使用
```python
# Site統計分析
site_stats_vba = device2bin_proc._analyze_site_statistics(
    ws, my_bin_array, vba_mode=True
)
site_stats_simple = device2bin_proc._analyze_site_statistics(
    ws, vba_mode=False
)

# Bin數據填充
device2bin_proc._fill_bin_data(
    summary_ws, ws,
    analysis_result=analysis_result,
    mode="accurate"
)

# Site統計添加
device2bin_proc._add_site_statistics(
    summary_ws, ws,
    vba_style=True
)
```

---

## 🔍 故障排除

### 常見問題

1. **找不到標題行**
   - 檢查是否包含"Index_No"關鍵字
   - 確認標題行在前10行內

2. **設備數據處理失敗**
   - 驗證Serial#列是否有數據
   - 檢查Bin#列格式是否正確

3. **Site統計異常**
   - 確認Site列存在且有有效數據
   - 檢查Site號是否為有效整數

4. **Summary工作表創建失敗**
   - 確認有足夠的測試數據
   - 檢查Bin統計是否正常

### 日誌監控
```python
# 關鍵日誌信息
"開始應用CTA8280格式..."
"找到標題行在第X行"
"總測試項目數量: X"
"開始應用Device2BinControl處理..."
"VBA 159-361行：核心分析完成"
"Summary工作表創建完成"
```

---

## 📈 性能指標

### 處理效能
- **FillEmptyItemName**：99.8%性能提升
- **快速模式**：SPD檔案處理速度提升50%
- **跳過清理**：大檔案處理時間減少30%

### 記憶體使用
- **最大行數限制**：65536行
- **最大列數限制**：300列
- **設備數量限制**：1000個設備

### 整合效果
- **代碼簡化**：減少400-500行重複代碼
- **方法減少**：11個重複方法→4個統一方法
- **維護性提升**：統一接口，減少bug風險

---

## 🏆 版本歷史

### v2.5.0 - Summary增強與代碼優化版本 (2025-01-22)
- ✅ **原始檔案連結追溯**：Summary工作表C1自動添加原始檔案連結
- ✅ **智能數據過濾**：只顯示有設備數量(>0)的測試項目，消除重複數據
- ✅ **Site統計精確比對**：Site統計能精確比對測項並填入統計數量和百分比
- ✅ **統一Site信息收集**：整合5個重複的Site查找方法為1個統一方法
- ✅ **大規模代碼清理**：移除16個未使用方法、2個重複定義，約400行冗餘代碼
- ✅ **確保All Pass顯示**：修復All Pass (Bin 1)可能不顯示的問題

### v2.4.0 - 重複功能整合版本
- ✅ 完成所有重複功能整合
- ✅ 統一接口設計
- ✅ 向後兼容性100%

### v2.3.0 - 功能增強版本
- ✅ Device2BinControl處理器完整實現
- ✅ Summary統計分析功能
- ✅ SPD檔案特殊處理

### v2.2.0 - 性能優化版本
- ✅ FillEmptyItemName 99.8%性能提升
- ✅ 快速模式和跳過清理功能

---

## 📚 相關文檔

- [converters_功能介紹.md](converters_功能介紹.md) - 轉換器功能說明
- [完整說明文檔.md](完整說明文檔.md) - 系統完整說明
- [config/settings.py](config/settings.py) - 配置參數說明

---

**最後更新**：2025-07-22
**整合狀態**：已完成 ✅
**測試狀態**：全部通過 ✅
**代碼優化**：已完成大規模清理 ✅
