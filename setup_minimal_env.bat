@echo off
echo 創建最小化虛擬環境用於打包...

REM 創建虛擬環境
python -m venv venv_minimal

REM 啟動虛擬環境
call venv_minimal\Scripts\activate.bat

REM 升級pip
python -m pip install --upgrade pip

REM 只安裝必要的套件
echo 安裝最小化依賴...
pip install pandas==2.0.3
pip install openpyxl==3.1.2
pip install pyinstaller

echo 檢查安裝的套件...
pip list

echo 虛擬環境設置完成！
echo 現在可以在這個環境中運行: pyinstaller main_optimized.spec
echo 或者運行: build_optimized.bat

pause
