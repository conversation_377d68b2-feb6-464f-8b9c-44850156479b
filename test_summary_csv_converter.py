#!/usr/bin/env python3
"""
測試Summary CSV轉換器功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.converters import get_summary_csv_converter
import pandas as pd

def test_summary_csv_converter():
    """測試Summary CSV轉換器功能"""
    
    print("🔧 測試Summary CSV轉換器")
    print("=" * 60)
    
    # 檢查測試文件
    test_files = [
        "GMT_G2304.csv",
        "G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv"
    ]
    
    available_files = [f for f in test_files if os.path.exists(f)]
    
    if not available_files:
        print("❌ 沒有找到測試文件")
        return
    
    # 獲取Summary CSV轉換器
    summary_csv_converter = get_summary_csv_converter()
    
    for test_file in available_files:
        print(f"\n📊 測試文件: {test_file}")
        print("-" * 50)
        
        # 檢查文件大小
        file_size = os.path.getsize(test_file)
        print(f"輸入文件大小: {file_size:,} bytes")
        
        try:
            # 執行Summary CSV轉換
            success, output_file = summary_csv_converter.convert(test_file)
            
            if success and output_file:
                print(f"✅ Summary CSV轉換成功: {output_file}")
                
                # 檢查輸出文件
                if os.path.exists(output_file):
                    output_size = os.path.getsize(output_file)
                    print(f"📊 輸出文件大小: {output_size:,} bytes")
                    
                    # 讀取並分析CSV內容
                    try:
                        df = pd.read_csv(output_file, encoding='utf-8-sig')
                        print(f"📈 CSV數據: {len(df)}行 × {len(df.columns)}列")
                        print(f"📝 列名: {', '.join(df.columns.tolist())}")
                        
                        # 顯示前幾行數據
                        if len(df) > 0:
                            print(f"📋 前3行數據:")
                            for i, row in df.head(3).iterrows():
                                print(f"   行{i+1}: {dict(row)}")
                        
                        # 檢查是否包含預期的統計數據
                        expected_columns = ['Bin', 'Count', '%', 'Definition']
                        found_columns = [col for col in expected_columns if col in df.columns]
                        print(f"✅ 找到預期列: {', '.join(found_columns)}")
                        
                        if 'Count' in df.columns:
                            total_count = df['Count'].sum() if df['Count'].dtype in ['int64', 'float64'] else "N/A"
                            print(f"📊 總設備數量: {total_count}")
                        
                    except Exception as e:
                        print(f"❌ 讀取CSV文件失敗: {e}")
                        
                else:
                    print(f"❌ 輸出文件不存在: {output_file}")
            else:
                print("❌ Summary CSV轉換失敗")
                
        except Exception as e:
            print(f"❌ 轉換測試失敗: {e}")
            import traceback
            print(f"錯誤詳情: {traceback.format_exc()}")
    
    print()
    print("=" * 60)
    print("🎉 Summary CSV轉換器測試完成")
    print()
    print("📋 新功能特點:")
    print("  🎯 專門產生Summary統計分析")
    print("  📊 輸出CSV格式，易於後續處理")
    print("  ⚡ 執行完整的三階段處理")
    print("  🔄 自動清理臨時文件")
    print("  📈 包含Bin統計、良率分析等")
    print()
    print("💡 使用場景:")
    print("  • 只需要統計分析數據，不需要完整Excel文件")
    print("  • 需要將統計數據導入其他系統")
    print("  • 批量處理時只關注統計結果")
    print("  • 節省存儲空間（CSV比Excel小很多）")
    print("=" * 60)

if __name__ == "__main__":
    test_summary_csv_converter()
