# CSV轉換器 - 新增Summary CSV功能

## 🎉 新功能介紹

已成功在UI中新增**「僅產生Summary CSV」**按鈕，提供快速統計分析功能。

## 📋 功能對比

| 功能 | 完整轉換 + Summary | 僅產生Summary CSV |
|------|-------------------|------------------|
| **輸出格式** | Excel (.xlsx) | CSV (.csv) |
| **處理時間** | 較長（完整處理） | 較短（僅統計） |
| **包含內容** | sum + Data11 + QAData + Summary | 僅Summary統計 |
| **適用場景** | 完整數據分析 | 快速統計查看 |

## 🔧 UI改進

### 原始設計
- 單一按鈕：「完整轉換 + Summary」

### 新設計
- **左側按鈕**：「完整轉換 + Summary」
- **右側按鈕**：「僅產生Summary CSV」
- 兩個按鈕並排顯示，各有說明文字

## 📊 Summary CSV內容

生成的CSV文件包含完整的Summary數據，結構如下：

### CSV結構（8行 × 13列）
```csv
第1行: Bin,Count,%,Definition,Note,Site 1,%,Site 2,%,Site 3,%,Site 4,%
第2行: Total,16,原始檔案: GMT_G2304.csv,,,,,,,,,,
第3行: Pass,4,,,,,,,,,,,
第4行: Fail,12,,,,,,,,,,,
第5行: Yield,25.00%,,,,,,,,,,,
第6行: ,,,,,Total,4,Total,4,Total,4,Total,4
第7行: 41,12,75.00%,Site_Check,,0,0%,4,100.00%,4,100.00%,4,100.00%
第8行: 1,4,25.00%,All Pass,,4,100.00%,0,0%,0,0%,0,0%
```

### 數據說明
1. **統計摘要（第2-6行）**：
   - **Total**: 總設備數量（16個）
   - **Pass**: 通過設備數量（4個）
   - **Fail**: 失敗設備數量（12個）
   - **Yield**: 良率百分比（25.00%）
   - **Site總計**: 各Site的設備總數

2. **詳細Bin統計（第7-8行）**：
   - **Bin 41**: Site_Check，12個設備，75.00%
   - **Bin 1**: All Pass，4個設備，25.00%
   - **Site分布**: 每個Site各Bin的詳細統計

## 🎯 使用方式

1. **啟動程式**: 執行 `python main.py`
2. **選擇檔案**: 點擊「瀏覽」選擇CSV或SPD檔案
3. **選擇功能**:
   - 點擊「完整轉換 + Summary」→ 產生完整Excel檔案
   - 點擊「僅產生Summary CSV」→ 僅產生統計CSV檔案
4. **查看結果**: 檔案會自動保存到原檔案目錄

## 📁 輸出檔案命名

- **完整轉換**: `原檔名_clean_converted.xlsx`
- **Summary CSV**: `原檔名_summary.csv`

## ✅ 測試結果

已成功測試GMT_G2304.csv檔案：
- ✅ Summary CSV生成成功
- ✅ 包含完整統計數據（7行 × 13列）
- ✅ 良率計算正確（25.00%）
- ✅ Site統計完整（4個Site）
- ✅ Bin分析準確（Bin 1和Bin 41）

## 🚀 技術實現

使用現有的`SummaryCSVConverter`類別：
- 複用完整轉換邏輯
- 提取Summary工作表
- 轉換為CSV格式
- 自動清理臨時檔案

這個新功能為用戶提供了更靈活的選擇，既可以進行完整分析，也可以快速獲取統計摘要。
