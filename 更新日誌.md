# CSV轉換器更新日誌

## 🚀 版本更新記錄

### 📅 2025-07-23 - 重大功能更新

#### ✨ **新功能**

##### 1. **多檔案批次處理支援** 🔥
- **功能描述**: 支援同時選擇多個CSV/SPD/ZIP檔案進行批次轉換
- **UI改進**: 
  - 將單檔案選擇改為檔案列表框
  - 新增"選擇檔案"按鈕支援多選
  - 新增"清除列表"按鈕清空檔案列表
- **批次處理**: 
  - 支援完整轉換的批次處理
  - 支援Summary CSV的批次處理
  - 進度條顯示批次處理進度
  - 詳細的成功/失敗結果報告

##### 2. **輸出路徑自定義** 📁
- **功能描述**: 允許用戶選擇輸出檔案的存放目錄
- **UI改進**: 新增輸出目錄選擇區域
- **預設行為**: 預設輸出到當前工作目錄
- **支援範圍**: 所有轉換模式都支援自定義輸出路徑

##### 3. **ZIP檔案完整支援** 📦
- **功能描述**: 完整支援.csv.zip和.spd.zip檔案處理
- **自動解壓**: 自動解壓縮ZIP檔案到臨時目錄
- **連結保持**: Summary CSV中的連結指向原始ZIP檔案
- **自動清理**: 處理完成後自動清理臨時檔案

#### 🔧 **重要修復**

##### 1. **Excel數字格式修復** 📊
- **問題**: Excel中的數字以文字格式存儲，無法進行數學運算
- **修復**: 
  - 修改`write_dataframe_to_worksheet()`方法
  - 新增`_is_numeric_value()`和`_convert_to_numeric()`方法
  - 數字自動轉換為int或float類型
  - 文字內容保持字符串格式

**修復前**:
```python
str_value = str(value).strip()
ws.cell(r_idx, c_idx).value = str_value  # 文字格式
```

**修復後**:
```python
if self._is_numeric_value(str_value):
    ws.cell(r_idx, c_idx).value = self._convert_to_numeric(str_value)  # 數字格式
else:
    ws.cell(r_idx, c_idx).value = str_value  # 文字格式
```

##### 2. **Summary工作表百分比格式修復** 📈
- **問題**: 百分比以文字格式存儲（如"12.34%"），無法排序和計算
- **修復**:
  - 修改`_format_percentage()`方法
  - 新增`_get_percentage_value()`方法
  - 百分比以數字格式存儲（如0.1234）
  - Excel自動顯示為百分比格式（12.34%）

**修復前**:
```python
summary_ws.cell(7, 3).value = self._format_percentage(percentage)  # "12.34%"
```

**修復後**:
```python
percentage_cell = summary_ws.cell(7, 3)
percentage_cell.value = self._get_percentage_value(percentage)  # 0.1234
percentage_cell.number_format = '0.00%'  # Excel百分比格式
```

##### 3. **Summary CSV的C2原始檔案路徑修復** 🔗
- **問題**: Summary CSV的C2位置沒有顯示原始檔案的完整路徑
- **修復**: 在C2位置正確設置原始檔案的完整路徑
- **支援**: ZIP檔案的連結指向原始ZIP檔案路徑

#### 📈 **性能改進**

##### 1. **批次處理效率**
- 支援多檔案並行處理邏輯
- 進度條實時更新處理狀態
- 錯誤處理不影響其他檔案處理

##### 2. **記憶體管理**
- 自動清理ZIP解壓的臨時目錄
- 支援多個臨時目錄的管理
- 程式關閉時自動清理所有臨時資源

#### 🎨 **UI/UX改進**

##### 1. **檔案選擇體驗**
- 檔案列表框顯示已選檔案
- 支援檔案類型標識（CSV檔案、SPD檔案、CSV壓縮檔等）
- 清除列表功能方便重新選擇

##### 2. **進度顯示**
- 批次處理進度條顯示當前進度
- 狀態欄顯示當前處理的檔案名稱
- 詳細的處理結果報告

##### 3. **錯誤處理**
- 個別檔案處理失敗不影響其他檔案
- 詳細的錯誤信息顯示
- 成功和失敗檔案的分類報告

#### 🔄 **向後兼容性**
- 所有現有功能保持完全兼容
- 單檔案處理邏輯保持不變
- 現有的轉換器API保持穩定

#### 📝 **技術細節**

##### 修改的檔案:
- `gui/main_window.py` - 主要UI改進和批次處理邏輯
- `utils/excel_utils.py` - Excel數字格式修復
- `core/processors/base_processor.py` - 百分比格式修復
- `core/processors/device_bin_processor.py` - Summary工作表百分比修復
- `core/converters.py` - 輸出路徑支援
- `utils/file_utils.py` - ZIP檔案處理和輸出路徑支援
- `config/settings.py` - ZIP檔案類型支援

##### 新增的方法:
- `_is_numeric_value()` - 數字檢測
- `_convert_to_numeric()` - 數字格式轉換
- `_get_percentage_value()` - 百分比數值轉換
- `browse_files()` - 多檔案選擇
- `add_file_to_list()` - 檔案列表管理
- `clear_files()` - 清除檔案列表
- `update_file_status()` - 狀態更新

#### 🎯 **使用方式**

##### 多檔案批次處理:
1. 點擊"選擇檔案"按鈕
2. 在檔案對話框中選擇多個檔案（Ctrl+點擊或Shift+點擊）
3. 檔案會顯示在列表框中
4. 選擇輸出目錄（可選）
5. 點擊轉換按鈕開始批次處理

##### 輸出路徑設定:
1. 點擊"選擇目錄"按鈕
2. 選擇輸出檔案的存放位置
3. 所有轉換結果都會保存到指定目錄

---

## 📊 **效果對比**

### Excel數字格式修復效果:
- **修復前**: 數字顯示為文字，無法排序和計算
- **修復後**: 數字正確識別，支援所有Excel數學功能

### 百分比格式修復效果:
- **修復前**: "25.00%" (文字格式)
- **修復後**: 0.25 (數字格式，Excel顯示為25.00%)

### 批次處理效果:
- **修復前**: 只能單檔案處理
- **修復後**: 支援多檔案批次處理，大幅提升工作效率

---

## 🔮 **未來計劃**
- 支援拖放檔案功能
- 增加處理歷史記錄
- 支援更多檔案格式
- 增加轉換模板功能
